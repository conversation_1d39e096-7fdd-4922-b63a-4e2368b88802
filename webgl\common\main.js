/**
 * 桂林智源 SVG 数字化系统 - 主页面脚本
 * 包含Unity WebGL界面切换功能
 */

// ==================== Unity WebGL 界面切换功能 ====================

// 全局变量 - 循环队列切换机制
const viewQueue = [
    {
        id: 'unity',
        name: 'Unity 3D',
        displayName: '3D界面',
        containerClass: 'show-unity',
        iframeSrc: '',
        isUnity: true
    },
    {
        id: 'electrical-topology',
        name: '电气拓扑',
        displayName: '电气拓扑页面',
        containerClass: 'show-electrical-topology',
        iframeSrc: 'http://*************/scada/topo/fullscreen?guid=5a4b41f3-0956-4e19-a861-58cb90a98549&type=3&date=' + new Date(),
        isUnity: false
    },
    {
        id: 'cooling-topology',
        name: '水冷拓扑',
        displayName: '水冷拓扑页面',
        containerClass: 'show-cooling-topology',
        iframeSrc: 'http://*************/scada/topo/fullscreen?guid=5e8934c3-2d94-41de-ab87-303f61a3c7f8&type=3&date=' + new Date(),
        isUnity: false
    }
];

let currentViewIndex = 0; // 当前页面在队列中的索引
let loadedPages = new Set(); // 记录已加载的页面

/**
 * 初始化Unity WebGL界面切换功能 - 循环队列机制
 */
function initUnityViewSwitcher() {
    console.log('初始化Unity WebGL界面切换功能（循环队列机制）...');

    // 设置初始状态
    switchToView(0, false, true); // 切换到第一个页面（Unity 3D），不显示加载动画，强制更新

    // 预加载所有页面
    preloadAllPages();

    // 设置键盘快捷键
    document.addEventListener('keydown', function (e) {
        if (e.ctrlKey && e.key === 'ArrowLeft') {
            e.preventDefault();
            switchToPreviousView();
        } else if (e.ctrlKey && e.key === 'ArrowRight') {
            e.preventDefault();
            switchToNextView();
        } else if (e.ctrlKey && e.key === '1') {
            e.preventDefault();
            switchToViewById('unity');
        } else if (e.ctrlKey && e.key === '2') {
            e.preventDefault();
            switchToViewById('electrical-topology');
        } else if (e.ctrlKey && e.key === '3') {
            e.preventDefault();
            switchToViewById('cooling-topology');
        }
    });

    console.log('Unity WebGL界面切换功能初始化完成');
}

/**
 * 预加载所有页面
 */
function preloadAllPages() {
    viewQueue.forEach(view => {
        if (!view.isUnity && view.iframeSrc && !loadedPages.has(view.id)) {
            preloadPage(view);
        }
    });
}

/**
 * 预加载指定页面
 */
function preloadPage(view) {
    const iframeId = `${view.id}-iframe`;
    const iframe = document.getElementById(iframeId);

    if (iframe && !loadedPages.has(view.id)) {
        iframe.src = view.iframeSrc;

        iframe.onload = function () {
            loadedPages.add(view.id);
            console.log(`${view.displayName}预加载完成`);
        };

        iframe.onerror = function () {
            console.error(`${view.displayName}加载失败`);
        };
    }
}

/**
 * 切换到上一个页面
 */
function switchToPreviousView() {
    const prevIndex = (currentViewIndex - 1 + viewQueue.length) % viewQueue.length;
    switchToView(prevIndex);
}

/**
 * 切换到下一个页面
 */
function switchToNextView() {
    const nextIndex = (currentViewIndex + 1) % viewQueue.length;
    switchToView(nextIndex);
}

/**
 * 根据页面ID切换到指定页面
 */
function switchToViewById(viewId) {
    const index = viewQueue.findIndex(view => view.id === viewId);
    if (index !== -1) {
        switchToView(index);
    }
}

/**
 * 切换到指定索引的页面
 */
function switchToView(index, showLoading = true, forceUpdate = false) {
    // 如果不是强制更新且索引相同，则跳过
    if (!forceUpdate && index === currentViewIndex) return;

    const targetView = viewQueue[index];
    if (!targetView) return;

    console.log(`切换到${targetView.displayName}`);

    if (showLoading) {
        showLoadingOverlay(`正在切换到${targetView.displayName}...`);
    }

    // 确保非Unity页面已加载
    if (!targetView.isUnity && !loadedPages.has(targetView.id)) {
        preloadPage(targetView);
    }

    const delay = showLoading ? 300 : 0;
    setTimeout(() => {
        const wrapper = document.getElementById('unityWrapper');
        if (wrapper) {
            wrapper.className = `unity-wrapper ${targetView.containerClass}`;
        }

        // 更新当前索引
        currentViewIndex = index;

        // 更新按钮状态
        updateArrowButtonStates();

        if (showLoading) {
            hideLoadingOverlay();
        }
    }, delay);
}

/**
 * 更新箭头按钮状态 - 循环队列机制
 */
function updateArrowButtonStates() {
    const prevArrow = document.getElementById('prev-arrow');
    const nextArrow = document.getElementById('next-arrow');

    if (!prevArrow || !nextArrow) return;

    const currentView = viewQueue[currentViewIndex];
    const prevIndex = (currentViewIndex - 1 + viewQueue.length) % viewQueue.length;
    const nextIndex = (currentViewIndex + 1) % viewQueue.length;
    const prevView = viewQueue[prevIndex];
    const nextView = viewQueue[nextIndex];

    // 重置按钮状态
    prevArrow.classList.remove('current-view');
    nextArrow.classList.remove('current-view');

    // 设置当前视图指示（左箭头表示当前视图）
    // prevArrow.classList.add('current-view');

    // 更新工具提示
    // prevArrow.title = `当前：${currentView.displayName} | 上一个：${prevView.name} (Ctrl+←)`;
    // nextArrow.title = `下一个：${nextView.name} (Ctrl+→)`;
    prevArrow.title = `Ctrl+←`;
    nextArrow.title = `Ctrl+→`;

    console.log(`当前页面：${currentView.displayName} (${currentViewIndex + 1}/${viewQueue.length})`);
}

/**
 * 显示加载覆盖层
 * @param {string} text - 加载文本
 */
function showLoadingOverlay(text = '正在加载...') {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        const loadingText = overlay.querySelector('.loading-text');
        if (loadingText) {
            loadingText.textContent = text;
        }
        overlay.classList.add('show');
    }
}

/**
 * 隐藏加载覆盖层
 */
function hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.remove('show');
    }
}

// ==================== 兼容性函数 ====================

/**
 * 兼容性函数：切换到Unity 3D界面
 */
function switchToUnity() {
    switchToViewById('unity');
}

/**
 * 兼容性函数：切换到电气拓扑页面
 */
function switchToTopology() {
    switchToViewById('electrical-topology');
}

// ==================== 原有功能代码 ====================
/**
 * 处理Unity WebGL嵌入、参数监控和故障信息显示
 */

// API配置已迁移到config.js中，使用统一的API管理

// 全局变量
let currentAlarmFilter = 'all'; // 当前筛选类型
let alarmRefreshInterval = null; // 自动刷新定时器
let lastAlarmUpdate = null; // 最后更新时间

// 配置常量
const ALARM_DISPLAY_CONFIG = {
    maxDisplayCount: 50, // 最大显示条数
    refreshInterval: 30000 // 刷新间隔（毫秒）
};

var unityInstance = null;
var unityIframe = null;

// 页面加载完成后初始化
window.addEventListener("load", function () {
    initMainPage();
    updateTime();
    setInterval(updateTime, 1000);
    // 初始化Unity WebGL界面切换功能
    initUnityViewSwitcher();
    // 初始化报警监控筛选
    initAlarmFilters();
    initRealTimeAlarmMonitor();
    // 初始化ECharts图表
    setTimeout(() => {
        console.log('开始初始化ECharts图表...');
        initCharts();
    }, 1500);
});

/**
 * 更新时间显示
 */
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

/**
 * 初始化主页面
 */
function initMainPage() {
    unityIframe = document.getElementById('unity-iframe');

    // 监听来自Unity iframe的消息
    window.addEventListener('message', function (event) {
        // 确保消息来源安全
        if (event.source !== unityIframe.contentWindow) {
            return;
        }

        if (event.data && event.data.type === 'unityLoaded') {
            console.log('Unity WebGL 加载完成');
            onUnityLoaded();
        }
    });

    // 绑定控制按钮事件
    bindControlEvents();

    // 绑定窗口大小变化事件
    window.addEventListener('resize', function () {
        resizeAllCharts();
    });

    // 添加按钮点击波纹效果
    addRippleEffect();
}

/**
 * Unity加载完成后的回调
 */
function onUnityLoaded() {
    // 初始化右侧面板的ECharts图表
    console.log('Unity加载完成，初始化右侧面板图表');
    // 图表初始化已在charts.js的initCharts函数中完成
}

/**
 * 绑定控制按钮事件
 */
function bindControlEvents() {
    // 绑定图表控制按钮事件
    bindChartControlEvents();

    // 绑定重置视角按钮事件（如果存在）
    const resetViewBtn = document.getElementById("reset-view-btn");
    if (resetViewBtn) {
        resetViewBtn.addEventListener("click", function () {
            resetUnityView();
        });
    }
}



/**
 * 绑定图表控制按钮事件
 */
function bindChartControlEvents() {
    const chartBtns = document.querySelectorAll('.chart-btn');
    chartBtns.forEach(btn => {
        btn.addEventListener('click', function () {
            // 移除其他按钮的active状态
            const parentControls = this.parentElement;
            parentControls.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
            // 添加当前按钮的active状态
            this.classList.add('active');
        });
    });
}

/**
 * 向Unity发送命令
 * @param {string} target - 目标GameObject名称
 * @param {string} method - 要调用的方法名
 * @param {string} parameter - 可选参数
 */
function sendUnityCommand(target, method, parameter) {
    try {
        if (unityIframe && unityIframe.contentWindow) {
            unityIframe.contentWindow.postMessage({
                type: 'unityCommand',
                target: target,
                method: method,
                parameter: parameter || ''
            }, '*');
            console.log('发送Unity命令:', target, method, parameter);
        } else {
            console.warn('Unity iframe 未准备就绪');
        }
    } catch (error) {
        console.error('发送Unity命令失败:', error);
    }
}

/**
 * 添加按钮点击波纹效果
 */
function addRippleEffect() {
    const buttons = document.querySelectorAll('.control-btn, .toolbar-btn, .chart-btn');
    buttons.forEach(button => {
        button.addEventListener('click', function (e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

/**
 * 初始化报警监控筛选功能
 */
function initAlarmFilters() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function () {
            // 移除其他按钮的active状态
            filterBtns.forEach(b => b.classList.remove('active'));
            // 添加当前按钮的active状态
            this.classList.add('active');

            const filter = this.getAttribute('data-filter');
            filterAlarmList(filter);
        });
    });
}

/**
 * 筛选报警监控列表
 * @param {string} filter - 筛选类型：all, alarm, fault
 */
function filterAlarmList(filter) {
    currentAlarmFilter = filter;
    const alarmItems = document.querySelectorAll('.alarm-item');
    alarmItems.forEach(item => {
        switch (filter) {
            case 'all':
                item.style.display = 'grid';
                break;
            case 'alarm':
                // 只显示报警事件
                if (item.classList.contains('alarm')) {
                    item.style.display = 'grid';
                } else {
                    item.style.display = 'none';
                }
                break;
            case 'fault':
                // 只显示故障事件
                if (item.classList.contains('fault')) {
                    item.style.display = 'grid';
                } else {
                    item.style.display = 'none';
                }
                break;
        }
    });
}

/**
 * 显示参数详情页面
 * @param {string} paramType - 参数类型
 */
function showParameterDetails(paramType) {
    console.log('显示参数详情:', paramType);

    const paramNames = {
        'current': 'SVG总电流',
        'voltage': 'SVG总电压',
        'power': '功率因数',
        'frequency': '系统频率',
        'temperature': '设备温度',
        'efficiency': '运行效率'
    };

    const paramName = paramNames[paramType] || paramType;
    console.log(`即将显示${paramName}参数的详细趋势曲线与历史数据`);
    // alert(`即将显示${paramName}参数的详细趋势曲线与历史数据`);
}

/**
 * 显示水冷系统详情
 */
function showCoolingDetails() {
    console.log('显示水冷系统详情');
    // alert('即将显示水冷系统详情页，查看实时数据曲线');
}

/**
 * 更新系统状态显示
 * @param {string} status - 状态：ready, fault, charging, waiting, running
 */
function updateSystemStatus(status) {
    // 移除所有状态项的active类
    const statusItems = document.querySelectorAll('.status-item');
    statusItems.forEach(item => item.classList.remove('active'));

    // 为指定状态添加active类
    const targetItem = document.querySelector(`[data-status="${status}"]`);
    if (targetItem) {
        targetItem.classList.add('active');
    }
}



/**
 * 获取状态中文名称
 */
function getStatusName(status) {
    const statusNames = {
        'ready': '就绪',
        'fault': '故障',
        'charging': '充电',
        'waiting': '合高压等待',
        'running': '运行'
    };
    return statusNames[status] || status;
}



// ==================== 新增：实时报警监控API集成 ====================

/**
 * 初始化实时报警监控
 */
function initRealTimeAlarmMonitor() {
    console.log('初始化实时报警监控...');

    // 立即加载一次数据
    loadRealTimeAlarmData();

    // 设置自动刷新
    alarmRefreshInterval = setInterval(() => {
        loadRealTimeAlarmData();
    }, ALARM_DISPLAY_CONFIG.refreshInterval);

    console.log(`实时报警监控初始化完成，自动刷新间隔：${ALARM_DISPLAY_CONFIG.refreshInterval / 1000}秒，最大显示：${ALARM_DISPLAY_CONFIG.maxDisplayCount}条`);
}

/**
 * 从API加载实时报警数据
 * 使用config.js中的统一API函数
 */
async function loadRealTimeAlarmData() {
    try {
        updateDataStatus('loading', '正在加载...');

        // 检查config.js中的函数是否可用
        if (typeof getRealTimeAlarmLogList !== 'function') {
            throw new Error('getRealTimeAlarmLogList函数未找到，请确保config.js正确加载');
        }

        console.log('开始调用统一API获取实时报警数据...');

        // 调用config.js中的统一API函数
        const result = await getRealTimeAlarmLogList({
            pageNum: 1,
            pageSize: 100
        });

        if (result.success) {
            // 使用统一API返回的处理后数据
            processRealTimeAlarmDataFromAPI(result);
            updateDataStatus('success', '数据加载成功');
            lastAlarmUpdate = new Date();
            updateLastUpdateTime();
            console.log(`实时报警数据加载成功，共${result.total}条记录，有效事件${result.data.length}条`);
        } else {
            throw new Error(result.message || '获取数据失败');
        }

    } catch (error) {
        console.error('加载实时报警数据失败:', error);
        let errorMessage = error.message;
        if (error.message.includes('CORS') || error.message.includes('fetch')) {
            errorMessage = 'CORS跨域请求被阻止，请联系管理员配置服务器允许跨域访问';
        }
        updateDataStatus('error', '加载失败: ' + errorMessage);
        showAlarmError(errorMessage);
    }
}

/**
 * 处理实时报警API数据（原始版本，保留兼容性）
 * @param {Object} data - API返回的原始数据
 */
function processRealTimeAlarmData(data) {
    const alarmList = document.querySelector('.alarm-list');
    if (!alarmList) {
        console.error('找不到alarm-list元素');
        return;
    }

    // 清空现有数据
    alarmList.innerHTML = '';

    if (!data.rows || data.rows.length === 0) {
        alarmList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-inbox"></i>
              <p>暂无报警事件</p>
            </div>
          `;
        return;
    }

    console.log('处理报警数据，总数:', data.rows.length);

    // 使用config.js中的统一数据处理函数
    if (typeof processAlarmLogData === 'function') {
        const processedEvents = processAlarmLogData(data.rows);

        // 限制显示数量
        const displayEvents = processedEvents.slice(0, ALARM_DISPLAY_CONFIG.maxDisplayCount);

        // 渲染事件列表
        renderRealTimeAlarmList(displayEvents);
    } else {
        console.error('processAlarmLogData函数未找到，请确保config.js正确加载');
    }
}

/**
 * 处理来自统一API的实时报警数据
 * @param {Object} result - 统一API返回的结果对象
 */
function processRealTimeAlarmDataFromAPI(result) {
    const alarmList = document.querySelector('.alarm-list');
    if (!alarmList) {
        console.error('找不到alarm-list元素');
        return;
    }

    // 清空现有数据
    alarmList.innerHTML = '';

    if (!result.data || result.data.length === 0) {
        alarmList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-inbox"></i>
              <p>暂无报警事件</p>
            </div>
          `;
        return;
    }

    console.log('处理统一API报警数据，总数:', result.total, '有效事件:', result.data.length);

    // 限制显示数量
    const displayEvents = result.data.slice(0, ALARM_DISPLAY_CONFIG.maxDisplayCount);

    // 渲染事件列表
    renderRealTimeAlarmList(displayEvents);
}

/**
 * 渲染实时报警列表
 * @param {Array} events - 事件数组
 */
function renderRealTimeAlarmList(events) {
    const alarmList = document.querySelector('.alarm-list');
    if (!alarmList) return;

    if (events.length === 0) {
        alarmList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-inbox"></i>
              <p>暂无符合条件的报警事件</p>
            </div>
          `;
        return;
    }

    const eventsHtml = events.map((event, index) => {
        // 确定事件类型的CSS类名
        const eventTypeClass = event.alertName === '报警设备' ? 'alarm' : 'fault';

        // 格式化时间显示（合并日期和时间为一列）
        const formattedTime = formatAlarmDisplayTime(event.createTime);

        // 倒序序号：最新的事件显示为序号1
        const serialNumber = events.length - index;

        return `
            <div class="alarm-item ${eventTypeClass}">
              <span class="alarm-serial">${serialNumber}</span>
              <span class="alarm-datetime">${formattedTime}</span>
              <span class="alarm-message" title="${event.eventName}">${event.eventName}</span>
            </div>
          `;
    }).join('');

    alarmList.innerHTML = eventsHtml;

    // 应用当前筛选条件
    filterAlarmList(currentAlarmFilter);
}

/**
 * 格式化报警时间显示
 * @param {string} timeString - 时间字符串
 * @returns {string} 格式化后的时间字符串
 */
function formatAlarmDisplayTime(timeString) {
    if (!timeString) return '';

    // 如果已经是正确格式，直接返回
    if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(timeString)) {
        return timeString;
    }

    // 尝试解析并格式化
    try {
        const date = new Date(timeString);
        if (isNaN(date.getTime())) {
            return timeString; // 如果解析失败，返回原字符串
        }

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
        console.warn('时间格式化失败:', error, timeString);
        return timeString;
    }
}

/**
 * 更新数据状态显示
 * @param {string} status - 状态：loading, success, error
 * @param {string} message - 状态消息
 */
function updateDataStatus(status, message) {
    const dataStatus = document.getElementById('dataStatus');
    if (dataStatus) {
        dataStatus.textContent = message;
        dataStatus.className = `data-status ${status}`;
    }
}

/**
 * 更新最后更新时间显示
 */
function updateLastUpdateTime() {
    const lastUpdateElement = document.getElementById('lastUpdate');
    if (lastUpdateElement && lastAlarmUpdate) {
        const timeStr = lastAlarmUpdate.toLocaleTimeString();
        lastUpdateElement.textContent = `最后更新: ${timeStr}`;
    }
}

/**
 * 显示报警错误信息
 * @param {string} message - 错误信息
 */
function showAlarmError(message) {
    const alarmList = document.querySelector('.alarm-list');
    if (alarmList) {
        alarmList.innerHTML = `
            <div class="empty-state error">
              <i class="fas fa-exclamation-triangle"></i>
              <p>${message}</p>
              <button class="retry-btn" onclick="loadRealTimeAlarmData()">
                <i class="fas fa-redo"></i>
                重新加载
              </button>
            </div>
          `;
    }
}

/**
 * 切换主菜单显示/隐藏
 */
function toggleMainMenu() {
    const dropdown = document.getElementById('mainMenuDropdown');
    dropdown.classList.toggle('show');

    // 点击其他地方关闭菜单
    document.addEventListener('click', function (event) {
        if (!event.target.closest('.header-center')) {
            dropdown.classList.remove('show');
        }
    });
}

/**
 * 导航到指定模块
 * @param {string} module - 模块名称
 */
function navigateToModule(module) {
    const dropdown = document.getElementById('mainMenuDropdown');
    dropdown.classList.remove('show');

    // 这里可以根据需要实现具体的导航逻辑
    console.log(`导航到模块: ${module}`);

    // 根据模块类型显示对应的弹窗
    switch (module) {
        case 'realtime-curve':
            showModuleModal('realtime-curve', '实时曲线', 'fas fa-chart-line', '实时数据.html');
            break;
        case 'history-record':
            showModuleModal('history-record', '历史记录', 'fas fa-history', '历史记录.html');
            break;
        case 'history-event':
            showModuleModal('history-event', '历史事件', 'fas fa-calendar-alt', '历史事件.html');
            break;
        case 'fault-wave':
            showModuleModal('fault-wave', '故障录波', 'fas fa-wave-square', '故障录波.html');
            break;
        case 'parameter-curve':
            showModuleModal('parameter-curve', '参数曲线', 'fas fa-chart-area', '参数曲线.html');
            break;
        case 'dsp':
            showModuleModal('dsp', 'DSP', 'fas fa-microchip', 'http://*************/scada/topo/fullscreen?guid=721de54b-bed8-43dc-9157-81ac6cff32a4&type=3&date=' + new Date());
            break;
        case 'version-info':
            showModuleModal('version-info', '版本信息', 'fas fa-info-circle', 'http://*************/scada/topo/fullscreen?guid=f166d35a-180f-4fbf-91ab-a63229da3391&type=3&date=' + new Date());
            break;
        default:
            console.warn(`未知模块: ${module}`);
    }
}

/**
 * 获取模块中文名称
 * @param {string} module - 模块英文名称
 * @returns {string} 中文名称
 */
function getModuleName(module) {
    const moduleNames = {
        'realtime-curve': '实时曲线',
        'history-record': '历史记录',
        'history-event': '历史事件',
        'fault-wave': '故障录波',
        'parameter-curve': '参数曲线',
        'version-info': '版本信息'
    };
    return moduleNames[module] || module;
}

/**
 * 打开水冷系统拓扑图
 */
function openCoolingTopology() {
    console.log('打开水冷系统拓扑图');
    // showTopologyModal('cooling');
    let date = new Date();
    showModuleModal('cooling-topology', '水冷系统拓扑图', 'fas fa-tint', 'http://*************/scada/topo/fullscreen?guid=bdd07113-f2fd-4744-88f0-a055916c976b&type=3&date=' + date);
}

/**
 * 打开SVG系统拓扑图
 */
function openSystemTopology() {
    console.log('打开SVG系统拓扑图');
    // showTopologyModal('electrical');
    let date = new Date();
    showModuleModal('electrical-topology', '电气系统拓扑图', 'fas fa-bolt', 'http://*************/scada/topo/fullscreen?guid=6f2b118e-1b27-4ebd-b1db-bcd08ce10bbf&type=3&date=' + date);
}

/**
 * 打开I/O状态页面
 */
function openIOStatus() {
    console.log('打开I/O状态页面');
    // showIOStatusModal();
    let date = new Date();
    showModuleModal('io-status', 'I/O状态监控', 'fas fa-plug', 'http://*************/scada/topo/fullscreen?guid=df533b38-98b3-4c1a-9a3a-b2d7884f7770&type=3&date=' + date);
}

/**
 * 打开单元状态页面
 */
function openUnitStatus() {
    console.log('打开单元状态页面');
    // showUnitStatusModal();
    let date = new Date();
    showModuleModal('unit-status', '单元状态监控', 'fas fa-microchip', 'http://*************/scada/topo/fullscreen?guid=bc305d60-29d2-4635-82bb-ead9b337b31d&type=3&wework_cfm_code=NOhs%2BuVWHo97Du860XjXIPC5tyE8DwbeJo3xoLtc8tn94QGaXR9LJW5VvFSCVJID5Fpwj6f%2FVjFU6LVqrXItFhckh8qcSUUqInRO3%2Fb3FD0Ee6bfED2vOqLVG6i2ymNIFQ7%2Fi%2BDxy8I7Xi5S1uPRzyxBjWWrv5w9p218BH1F2vIV&date=' + date);
}

/**
 * 打开主控|辅控页面
 */
function openMasterControl() {
    console.log('打开主控|辅控页面');
    let date = new Date();
    showModuleModal('master-control', '主控|辅控', 'fas fa-sitemap', 'http://*************/scada/topo/fullscreen?guid=6f1379ce-d7b5-4017-9d9d-78d49813cd8c&type=3&date=' + date);
}

/**
 * 打开调试参数1页面
 */
function openDebugParams1() {
    console.log('打开调试参数1页面');
    showModuleModal('debug-params-1', '调试参数1', 'fas fa-cogs', '调试参数1.html');
}

/**
 * 打开调试参数2页面
 */
function openDebugParams2() {
    console.log('打开调试参数2页面');
    showModuleModal('debug-params-2', '调试参数2', 'fas fa-tools', '调试参数2.html');
}

/**
 * 重置Unity视角
 */
function resetUnityView() {
    sendUnityCommand("Main Camera", "SwitchToOverviewPosition");
    console.log('重置Unity视角');
}

/**
 * 显示I/O状态弹窗
 */
function showIOStatusModal() {
    const modal = document.getElementById('ioStatusModal');
    if (modal) {
        modal.style.display = 'flex';
        // 添加显示动画
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        // 模拟I/O状态更新
        simulateIOStatusUpdate();
    }
}

/**
 * 关闭I/O状态弹窗
 */
function closeIOStatusModal() {
    const modal = document.getElementById('ioStatusModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }
}

/**
 * 模拟I/O状态更新
 */
function simulateIOStatusUpdate() {
    // 随机更新一些I/O状态
    const indicators = document.querySelectorAll('#ioStatusModal .io-indicator');

    // 定期更新状态
    const updateInterval = setInterval(() => {
        // 随机选择几个指示器进行状态切换
        const randomIndicators = Array.from(indicators)
            .sort(() => 0.5 - Math.random())
            .slice(0, Math.floor(Math.random() * 5) + 1);

        randomIndicators.forEach(indicator => {
            if (Math.random() > 0.7) { // 30%的概率切换状态
                if (indicator.classList.contains('active')) {
                    indicator.classList.remove('active');
                    indicator.classList.add('inactive');
                } else {
                    indicator.classList.remove('inactive');
                    indicator.classList.add('active');
                }
            }
        });
    }, 3000);

    // 当弹窗关闭时停止更新
    const modal = document.getElementById('ioStatusModal');
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                if (modal.style.display === 'none') {
                    clearInterval(updateInterval);
                    observer.disconnect();
                }
            }
        });
    });
    observer.observe(modal, { attributes: true });
}

/**
 * 显示拓扑图弹窗
 * @param {string} type - 拓扑图类型：'electrical' 或 'cooling'
 */
function showTopologyModal(type) {
    const modal = document.getElementById('topologyModal');
    const title = document.getElementById('topologyModalTitle');
    const image = document.getElementById('topologyImage');
    const imageContainer = document.querySelector('.topology-image-container');
    const iframeContainer = document.getElementById('topologyIframeContainer');
    const iframe = document.getElementById('topologyIframe');

    if (modal && title && image && imageContainer && iframeContainer && iframe) {
        // 设置标题和内容
        if (type === 'electrical') {
            title.innerHTML = '<i class="fas fa-bolt"></i>电气系统拓扑图';
            image.src = './image/SVG系统拓扑图.png';
            image.alt = '电气系统拓扑图';
            // 显示图片，隐藏iframe
            imageContainer.style.display = 'block';
            iframeContainer.style.display = 'none';
        } else if (type === 'cooling') {
            title.innerHTML = '<i class="fas fa-tint"></i>水冷系统拓扑图';
            // 设置iframe URL
            iframe.src = 'http://*************/scada/topo/fullscreen?guid=29bd5260-38c5-4b4f-97a0-66e97903f9e9';
            // 隐藏图片，显示iframe
            imageContainer.style.display = 'none';
            iframeContainer.style.display = 'block';
        }

        modal.style.display = 'flex';
        // 添加显示动画
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }
}

/**
 * 关闭拓扑图弹窗
 */
function closeTopologyModal() {
    const modal = document.getElementById('topologyModal');
    const iframe = document.getElementById('topologyIframe');

    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
            // 清理iframe内容以释放资源
            if (iframe) {
                iframe.src = '';
            }
        }, 300);
    }
}

/**
 * 显示单元状态弹窗
 */
function showUnitStatusModal() {
    const modal = document.getElementById('unitStatusModal');
    if (modal) {
        modal.style.display = 'flex';
        // 添加显示动画
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        // 初始化单元状态数据
        initUnitStatusData();
    }
}

/**
 * 关闭单元状态弹窗
 */
function closeUnitStatusModal() {
    const modal = document.getElementById('unitStatusModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }
}

/**
 * 显示模块弹窗（修改为在新标签页打开）
 * @param {string} moduleId - 模块ID
 * @param {string} title - 弹窗标题
 * @param {string} iconClass - 图标类名
 * @param {string} iframeUrl - 页面URL地址
 */
function showModuleModal(moduleId, title, iconClass, iframeUrl = 'https://pic.rmb.bdstatic.com/bjh/down/bddf6d05be23936f9765bbe668e1fa41.gif') {
    // 在新标签页中打开页面
    window.open(iframeUrl, '_blank');

    console.log(`在新标签页打开${title}页面`);
}

/**
 * 关闭模块弹窗（已不再使用，保留函数以兼容现有代码）
 */
function closeModuleModal() {
    // 由于模块内容现在在新标签页中打开，此函数不再需要执行任何操作
    console.log('closeModuleModal: 此功能已不再使用');
    // 保留空函数以兼容可能的现有调用
}

/**
 * 关闭单元详细信息
 */
function closeUnitDetail() {
    // 取消选中状态
    const selectedUnit = document.querySelector('.unit-item.selected');
    if (selectedUnit) {
        selectedUnit.classList.remove('selected');
    }
    // 隐藏详细信息面板（可以添加动画效果）
    const detailSection = document.querySelector('.unit-detail-section');
    if (detailSection) {
        detailSection.style.opacity = '0.5';
        setTimeout(() => {
            detailSection.style.opacity = '1';
        }, 200);
    }
}

/**
 * 初始化单元状态数据和交互
 */
function initUnitStatusData() {
    // 为单元项添加点击事件
    const unitItems = document.querySelectorAll('.unit-item');
    unitItems.forEach(item => {
        item.addEventListener('click', function () {
            // 移除其他选中状态
            unitItems.forEach(u => u.classList.remove('selected'));
            // 添加选中状态
            this.classList.add('selected');

            // 更新详细信息
            const unitId = this.dataset.unit;
            updateUnitDetail(unitId);
        });
    });

    // 模拟实时数据更新
    simulateUnitStatusUpdate();
}

/**
 * 更新单元详细信息
 * @param {string} unitId - 单元ID
 */
function updateUnitDetail(unitId) {
    const titleElement = document.getElementById('selectedUnitTitle');
    if (titleElement) {
        titleElement.textContent = `${unitId} 单元详细状态`;
    }

    // 模拟不同单元的状态数据
    const statusData = getUnitStatusData(unitId);
    const statusItems = document.querySelectorAll('.unit-detail-section .status-item');

    statusItems.forEach((item, index) => {
        const indicator = item.querySelector('.status-indicator');
        if (indicator && statusData[index] !== undefined) {
            if (statusData[index]) {
                indicator.classList.remove('inactive');
                indicator.classList.add('active');
            } else {
                indicator.classList.remove('active');
                indicator.classList.add('inactive');
            }
        }
    });
}

/**
 * 获取单元状态数据（模拟数据）
 * @param {string} unitId - 单元ID
 * @returns {Array} 状态数组
 */
function getUnitStatusData(unitId) {
    // 模拟不同单元的状态数据
    const statusPatterns = {
        'A01': [false, false, false, false, false, false, true, false, false, false, false, false, false, false, false, false],
        'A02': [false, false, false, false, false, false, true, false, false, false, false, false, false, false, false, false],
        'A03': [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false],
        'A12': [false, false, false, false, false, false, true, false, false, false, false, false, false, false, false, false]
    };

    return statusPatterns[unitId] || statusPatterns['A12'];
}

/**
 * 模拟单元状态实时更新
 */
function simulateUnitStatusUpdate() {
    const updateInterval = setInterval(() => {
        // 随机更新单元电压值
        const unitItems = document.querySelectorAll('.unit-item');
        unitItems.forEach(item => {
            const voltageSpan = item.querySelector('.unit-voltage');
            if (voltageSpan && Math.random() > 0.8) { // 20%概率更新
                const baseVoltage = parseInt(voltageSpan.textContent);
                const variation = Math.floor(Math.random() * 6) - 3; // -3到+3的变化
                const newVoltage = Math.max(735, Math.min(745, baseVoltage + variation));
                voltageSpan.textContent = newVoltage.toString();
            }
        });

        // 随机更新指示器状态
        const indicators = document.querySelectorAll('.unit-item .indicator-dot');
        indicators.forEach(indicator => {
            if (Math.random() > 0.9) { // 10%概率切换状态
                if (indicator.classList.contains('active')) {
                    indicator.classList.remove('active');
                    indicator.classList.add('inactive');
                } else {
                    indicator.classList.remove('inactive');
                    indicator.classList.add('active');
                }
            }
        });

        // 更新电压统计显示
        updateVoltageStats();
    }, 2000);

    // 当弹窗关闭时停止更新
    const modal = document.getElementById('unitStatusModal');
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                if (modal.style.display === 'none') {
                    clearInterval(updateInterval);
                    observer.disconnect();
                }
            }
        });
    });
    observer.observe(modal, { attributes: true });
}

/**
 * 更新电压统计显示
 */
function updateVoltageStats() {
    const voltageValues = document.querySelectorAll('.voltage-value');
    voltageValues.forEach(valueElement => {
        if (Math.random() > 0.7) { // 30%概率更新
            const currentValue = parseInt(valueElement.textContent);
            const variation = Math.floor(Math.random() * 4) - 2; // -2到+2的变化
            const newValue = Math.max(735, Math.min(745, currentValue + variation));
            valueElement.textContent = `${newValue} V`;
        }
    });
} 


// ==================== 页面初始化 ====================

/**
 * 页面初始化函数
 */
function initializePage() {
    console.log('开始初始化页面...');

    try {
        // 初始化 Unity WebGL 界面切换功能
        initUnityViewSwitcher();
        console.log('页面初始化完成');

    } catch (error) {
        console.error('页面初始化失败:', error);
        logErrorEvent(error.message, '页面初始化失败');
    }
}

/**
 * 初始化现有的 parameter-item 元素的 tooltip
 */
function initializeExistingParameterTooltips() {
    console.log('初始化现有的 parameter-item tooltip...');

    const parameterItems = document.querySelectorAll('.parameter-item');
    let initializedCount = 0;

    parameterItems.forEach(item => {
        // 检查是否已经初始化过
        if (item.getAttribute('data-tooltip-initialized') === 'true') {
            return;
        }

        // 清理可能存在的旧 tooltip
        const existingTooltip = item.querySelector('.parameter-tooltip');
        if (existingTooltip) {
            existingTooltip.remove();
        }

        // 创建新的 tooltip 元素
        const tooltip = document.createElement('div');
        tooltip.className = 'parameter-tooltip';
        item.appendChild(tooltip);

        // 标记已初始化
        item.setAttribute('data-tooltip-initialized', 'true');

        // 初始化 tooltip 行为
        initializeParameterTooltipBehavior(item, tooltip);

        // 设置默认内容
        const labelElement = item.querySelector('.parameter-label');
        const valueElement = item.querySelector('.parameter-value');
        if (labelElement && valueElement) {
            const defaultText = `${labelElement.textContent} ${valueElement.textContent}`;
            tooltip.textContent = defaultText;
        }

        initializedCount++;
    });

    console.log(`已初始化 ${initializedCount} 个 parameter-item tooltip`);
}

/**
 * 启用 tooltip 调试模式
 */
function enableTooltipDebug() {
    window.tooltipDebug = true;
    tooltipDebugMode = true;
    console.log('Tooltip 调试模式已启用');
    console.log('点击任意 parameter-item 查看其 tooltip 状态');

    // 启动恢复机制
    startTooltipRecoveryMechanism();
}

/**
 * 禁用 tooltip 调试模式
 */
function disableTooltipDebug() {
    window.tooltipDebug = false;
    tooltipDebugMode = false;
    console.log('Tooltip 调试模式已禁用');

    // 停止恢复机制
    if (tooltipRecoveryInterval) {
        clearInterval(tooltipRecoveryInterval);
        tooltipRecoveryInterval = null;
    }
}

/**
 * 启动 tooltip 恢复机制
 */
function startTooltipRecoveryMechanism() {
    if (tooltipRecoveryInterval) {
        clearInterval(tooltipRecoveryInterval);
    }

    // 每30秒检查一次 tooltip 状态
    tooltipRecoveryInterval = setInterval(() => {
        const parameterItems = document.querySelectorAll('.parameter-item[data-tooltip-initialized="true"]');
        let recoveredCount = 0;

        parameterItems.forEach(item => {
            const tooltip = item.querySelector('.parameter-tooltip');
            if (tooltip) {
                const hasShowClass = tooltip.classList.contains('show');
                const isVisible = tooltip.style.visibility === 'visible';
                const opacity = tooltip.style.opacity;

                // 检测异常状态：有 show 类但不可见，或者透明度异常
                if ((hasShowClass && tooltip.style.visibility === 'hidden') ||
                    (hasShowClass && opacity === '0' && isVisible)) {

                    console.warn('检测到异常的 tooltip 状态，正在恢复...', {
                        element: item,
                        hasShowClass,
                        visibility: tooltip.style.visibility,
                        opacity: opacity
                    });

                    // 强制重置状态
                    tooltip.classList.remove('show');
                    tooltip.style.visibility = 'hidden';
                    tooltip.style.opacity = '0';
                    recoveredCount++;
                }
            }
        });

        if (recoveredCount > 0) {
            console.log(`恢复了 ${recoveredCount} 个异常的 tooltip`);
        }
    }, 30000);
}

/**
 * 手动修复所有 tooltip
 */
function fixAllTooltips() {
    const parameterItems = document.querySelectorAll('.parameter-item[data-tooltip-initialized="true"]');
    let fixedCount = 0;

    parameterItems.forEach(item => {
        const tooltip = item.querySelector('.parameter-tooltip');
        if (tooltip) {
            // 强制重置所有状态
            tooltip.classList.remove('show');
            tooltip.style.visibility = 'hidden';
            tooltip.style.opacity = '0';
            tooltip.style.left = '';
            tooltip.style.top = '';
            fixedCount++;
        }
    });

    console.log(`已修复 ${fixedCount} 个 tooltip 的状态`);
    return fixedCount;
}

/**
 * 获取 tooltip 系统状态
 */
function getTooltipSystemStatus() {
    const parameterItems = document.querySelectorAll('.parameter-item[data-tooltip-initialized="true"]');
    const status = {
        totalItems: parameterItems.length,
        itemsWithTooltip: 0,
        visibleTooltips: 0,
        abnormalStates: 0,
        details: []
    };

    parameterItems.forEach((item, index) => {
        const tooltip = item.querySelector('.parameter-tooltip');
        if (tooltip) {
            status.itemsWithTooltip++;

            const hasShowClass = tooltip.classList.contains('show');
            const isVisible = tooltip.style.visibility === 'visible';
            const opacity = tooltip.style.opacity;

            if (hasShowClass && isVisible) {
                status.visibleTooltips++;
            }

            // 检测异常状态
            const isAbnormal = (hasShowClass && tooltip.style.visibility === 'hidden') ||
                (hasShowClass && opacity === '0' && isVisible);

            if (isAbnormal) {
                status.abnormalStates++;
            }

            status.details.push({
                index,
                hasTooltip: true,
                hasShowClass,
                visibility: tooltip.style.visibility,
                opacity,
                isAbnormal
            });
        } else {
            status.details.push({
                index,
                hasTooltip: false
            });
        }
    });

    return status;
}

/**
 * 通用的 tooltip 行为初始化函数
 * @param {HTMLElement} parameterItem - parameter-item 元素
 * @param {HTMLElement} tooltip - tooltip 元素
 */
function initializeParameterTooltipBehavior(parameterItem, tooltip) {
    // 状态管理对象，确保状态同步
    const state = {
        showTimeout: null,
        hideTimeout: null,
        animationFrame: null,
        isVisible: false,
        isShowing: false,
        isHiding: false
    };

    // 重置所有状态的函数
    const resetState = () => {
        if (state.showTimeout) {
            clearTimeout(state.showTimeout);
            state.showTimeout = null;
        }
        if (state.hideTimeout) {
            clearTimeout(state.hideTimeout);
            state.hideTimeout = null;
        }
        if (state.animationFrame) {
            cancelAnimationFrame(state.animationFrame);
            state.animationFrame = null;
        }
        state.isShowing = false;
        state.isHiding = false;
    };

    // 强制同步状态的函数
    const syncState = () => {
        const hasShowClass = tooltip.classList.contains('show');
        const isVisuallyVisible = tooltip.style.visibility === 'visible' && hasShowClass;

        // 如果状态不一致，强制同步
        if (state.isVisible !== isVisuallyVisible) {
            console.warn('Tooltip state mismatch detected, syncing...', {
                stateVisible: state.isVisible,
                visuallyVisible: isVisuallyVisible,
                hasShowClass: hasShowClass
            });

            state.isVisible = isVisuallyVisible;
            if (!isVisuallyVisible) {
                tooltip.classList.remove('show');
                tooltip.style.visibility = 'hidden';
                tooltip.style.opacity = '0';
            }
        }
    };

    // 显示 tooltip 的函数
    const showTooltip = () => {
        // 清理隐藏相关的定时器
        if (state.hideTimeout) {
            clearTimeout(state.hideTimeout);
            state.hideTimeout = null;
        }

        // 同步状态检查
        syncState();

        // 如果已经可见或正在显示，直接返回
        if (state.isVisible || state.isShowing) return;

        state.isShowing = true;
        state.isHiding = false;

        // 先显示 tooltip 以获取正确的尺寸
        tooltip.style.opacity = '0';
        tooltip.style.visibility = 'visible';

        // 使用 requestAnimationFrame 确保 DOM 更新完成
        state.animationFrame = requestAnimationFrame(() => {
            // 检查是否在动画执行前被取消
            if (!state.isShowing) {
                tooltip.style.visibility = 'hidden';
                return;
            }

            try {
                const rect = parameterItem.getBoundingClientRect();
                const tooltipRect = tooltip.getBoundingClientRect();

                // 计算 tooltip 位置，确保不超出视窗边界
                let left = rect.left + rect.width / 2 - tooltipRect.width / 2;
                let top = rect.top - tooltipRect.height - 10;

                // 边界检查
                if (left < 10) left = 10;
                if (left + tooltipRect.width > window.innerWidth - 10) {
                    left = window.innerWidth - tooltipRect.width - 10;
                }
                if (top < 10) {
                    top = rect.bottom + 10; // 如果上方空间不足，显示在下方
                }

                tooltip.style.left = left + 'px';
                tooltip.style.top = top + 'px';

                // 再次检查是否仍然需要显示
                if (state.isShowing) {
                    tooltip.classList.add('show');
                    state.isVisible = true;
                }
            } catch (error) {
                console.warn('Tooltip positioning error:', error);
                tooltip.style.visibility = 'hidden';
            }

            state.isShowing = false;
            state.animationFrame = null;
        });
    };

    // 隐藏 tooltip 的函数
    const hideTooltip = () => {
        // 清理显示相关的定时器和动画
        if (state.showTimeout) {
            clearTimeout(state.showTimeout);
            state.showTimeout = null;
        }
        if (state.animationFrame) {
            cancelAnimationFrame(state.animationFrame);
            state.animationFrame = null;
        }

        state.isShowing = false;

        // 同步状态检查
        syncState();

        if (!state.isVisible || state.isHiding) return;

        state.isHiding = true;
        tooltip.classList.remove('show');
        state.isVisible = false;

        // 延迟隐藏，等待动画完成
        state.hideTimeout = setTimeout(() => {
            if (state.isHiding) {  // 确保没有被新的显示操作打断
                tooltip.style.visibility = 'hidden';
                tooltip.style.opacity = '0';
                state.isHiding = false;
            }
        }, 300);
    };

    // 绑定鼠标事件（只绑定一次）
    parameterItem.addEventListener('mouseenter', () => {
        resetState();
        state.showTimeout = setTimeout(showTooltip, 200); // 200ms 延迟显示
    });

    parameterItem.addEventListener('mouseleave', () => {
        resetState();
        hideTooltip();
    });

    // 防止快速移动鼠标时的闪烁
    tooltip.addEventListener('mouseenter', () => {
        if (state.hideTimeout) {
            clearTimeout(state.hideTimeout);
            state.hideTimeout = null;
            state.isHiding = false;
        }
    });

    tooltip.addEventListener('mouseleave', () => {
        hideTooltip();
    });

    // 添加调试功能（可选）
    if (window.tooltipDebug) {
        parameterItem.addEventListener('click', () => {
            console.log('Tooltip state:', {
                ...state,
                hasShowClass: tooltip.classList.contains('show'),
                visibility: tooltip.style.visibility,
                opacity: tooltip.style.opacity
            });
        });
    }
}

/**
 * 更新初始电气系统状态（仅状态指示器，不包含数值）
 */
function updateInitialElectricalStatus() {
    console.log('设置电气系统初始状态');

    // 设置所有状态为未激活状态
    const statusElements = ['ready-status', 'fault-status', 'running-status', 'standby-status', 'hv-wait-status'];
    statusElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            element.classList.remove('active');
            element.classList.add('inactive');
        }
    });

    // 清空数值显示，显示等待数据状态，并设置为红色
    const valueElements = [
        'load-reactive-power-value',
        'power-factor-value',
        'grid-reactive-current-value',
        'bus-voltage-uab-value',
        'bus-voltage-ubc-value',
        'bus-voltage-uca-value',
        'svg-current-ia-value',
        'svg-current-ib-value',
        'svg-current-ic-value'
    ];

    valueElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = '等待数据...';
            element.style.color = '#ff4444'; // 设置为红色警告颜色
        }
    });
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePage);
} else {
    // 如果页面已经加载完成，直接初始化
    initializePage();
}
