# TouchMain MQTT 实时数据订阅功能集成说明

在 `touchmain.html` 文件中集成了完整的 MQTT 实时数据订阅功能，实现工业监控界面的数据自动刷新。

## ✅ 已完成功能

### 1. MQTT 连接配置
- **订阅主题**: `/189/D19QBHKRZ791U/ws/service`
- **连接方式**: 与其他订阅主题保持一致的配置
- **自动重连**: 支持断线重连机制，最多重试5次
- **连接状态**: 实时显示连接状态和重连进度

### 2. 实时数据更新功能

#### 2.1 系统运行状态区域（5种状态显示）
- **支持的状态**: 就绪、运行、故障、备用、合高压等待
- **状态映射关系**:
  - `HMI_30039_4` → "就绪" 状态指示器 (`ready-status`)
  - `HMI_30039_5` → "运行" 状态指示器 (`running-status`)
  - `HMI_30039_6` → "故障" 状态指示器 (`fault-status`)
  - `HMI_30039_9` → "备用" 状态指示器 (`standby-status`)
  - `HMI_30039_10` → "合高压等待" 状态指示器 (`hv-wait-status`)
- **显示逻辑**: 根据接收到的数据值（0/1）控制对应状态指示器的激活状态

#### 2.2 网侧负载无功电流图表
- **数据源**: `HMI_32046` 参数
- **更新频率**: 每5秒自动更新一次
- **历史数据**: 维护最多60个数据点的滚动显示
- **图表类型**: 使用 ECharts 实时曲线图
- **数据处理**: 新数据到达时自动添加到图表，超过60个点时移除最旧数据
- **等待状态**: 在接收到真实数据前显示等待提示，接收到数据后自动移除

#### 2.3 系统关键参数区域
- **布局**: 保持现有的2-3列网格布局不变
- **实时更新参数列表**:
  - 母线电压Uab: `HMI_32030` (单位: kV, 2位小数)
  - 母线电压Ubc: `HMI_32032` (单位: kV, 2位小数)
  - 母线电压Uca: `HMI_32034` (单位: kV, 2位小数)
  - SVG电流Ia: `HMI_32040` (单位: A, 1位小数)
  - SVG电流Ib: `HMI_32042` (单位: A, 1位小数)
  - SVG电流Ic: `HMI_32044` (单位: A, 1位小数)
  - 网侧负载无功电流: `HMI_32046` (单位: A, 1位小数)
  - 负载无功功率: `HMI_32048` (单位: kVar, 2位小数)
  - 功率因数: `HMI_32050` (无单位, 3位小数)
- **数值格式**: 自动格式化显示，添加相应单位

### 3. 状态指示器功能
- **位置**: `footer-status-info` 区域
- **显示内容**:
  - MQTT 连接状态指示器（绿色=已连接，红色=断开，黄色=连接中）
  - 数据通信状态指示器（显示最后接收数据的时间戳）
  - 数据接收计数器（显示接收到的消息总数）

### 4. 代码架构实现
- **核心功能模块**: 在 `common/parameter-config.js` 中实现了 `TouchMainDataManager` 类
  - MQTT 连接管理
  - 数据解析和处理
  - 参数映射和更新
  - 图表数据管理
  - 状态指示器控制
- **UI 控制逻辑**: 在 `touchmain.html` 中集成数据更新逻辑
- **兼容性保证**: 不影响现有的控制按钮、导航功能和其他交互元素
- **性能优化**: 避免频繁的 DOM 操作，使用批量更新机制

### 5. 数据格式处理
- **消息格式**: 支持 MQTT 消息为 JSON 格式，包含 message 数组
- **数据验证**: 添加接收数据的有效性检查，无效数据不更新界面
- **时间戳**: 每次数据更新时记录时间戳用于状态显示
- **错误处理**: 完善的错误处理和日志记录机制

### 6. 测试和调试
- **测试页面**: 创建了 `mqtt-touchmain-test.html` 独立测试页面
- **调试功能**: 提供丰富的控制台调试信息和状态查询功能
- **连接测试**: 验证 MQTT 连接的稳定性和重连机制
- **界面测试**: 确认数据更新不会影响现有界面布局和功能

## 🔧 技术实现细节

### TouchMainDataManager 类主要方法：
- `init()`: 初始化 MQTT 连接
- `connectMQTT()`: 连接 MQTT 服务器
- `subscribeToTopic()`: 订阅数据主题
- `processRealtimeData()`: 处理实时数据
- `updateSystemStatus()`: 更新系统状态指示器
- `updateParameterValues()`: 更新参数值显示
- `updateChartData()`: 更新图表数据
- `startChartUpdateTimer()`: 启动图表更新定时器
- `getConnectionStatus()`: 获取连接状态信息

### 全局函数：
- `initTouchMainDataManager()`: 初始化数据管理器
- `updateTouchGridCurrentChart()`: 更新图表显示
- `updateTouchDataTimestamp()`: 更新数据时间戳

## 🚀 使用方法

1. **自动初始化**: 页面加载时自动初始化所有功能
2. **状态监控**: 通过底部状态栏监控连接状态和数据接收情况
3. **调试功能**: 使用浏览器控制台查看详细日志和状态信息

### 调试命令：
```javascript
// 获取连接状态
window.touchMainDataManager.getConnectionStatus()

// 手动重连
window.touchMainDataManager.reconnect()

// 查看当前数据
window.touchLatestElectricalData
```

## 📋 注意事项

1. **兼容性**: 完全兼容现有功能，不影响控制按钮和其他交互
2. **性能**: 优化了数据更新频率，避免过度刷新
3. **错误处理**: 具备完善的错误处理和恢复机制
4. **扩展性**: 代码结构支持后续功能扩展和参数添加