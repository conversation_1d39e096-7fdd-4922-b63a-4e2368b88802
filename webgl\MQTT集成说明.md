在 `touchmain.html` 文件中增加一个mqtt主题订阅，用来实现数据刷新。

## 功能特性

### 1. 数据订阅主题
subscribeTopic: /189/D19QBHKRZ791U/ws/service

### 2. 数据显示更新

#### 系统运行状态区域（5种状态显示）

- 支持的状态：就绪、运行、故障、备用、合高压等待
- 状态映射：
  - `HMI_30039_4`: 就绪状态
  - `HMI_30039_5`: 运行状态
  - `HMI_30039_6`: 故障状态
  - `HMI_30039_9`: 备用状态
  - `HMI_30039_10`: 合高压等待状态

#### 网侧负载无功电流图表区域
- 实时更新图表数据，使用 `HMI_32046` 参数
- 每5秒自动更新一次图表
- 支持最多60个数据点的历史显示

#### 系统关键参数区域（2-3列网格布局）
更新的参数包括：
- 母线电压Uab (`HMI_32030`)
- 母线电压Ubc (`HMI_32032`)
- 母线电压Uca (`HMI_32034`)
- SVG电流Ia (`HMI_32040`)
- SVG电流Ib (`HMI_32042`)
- SVG电流Ic (`HMI_32044`)
- 网侧负载无功电流 (`HMI_32046`)
- 负载无功功率 (`HMI_32048`)
- 功率因数 (`HMI_32050`)

### 3. 状态指示器

在 `footer-status-info` 区域显示 MQTT 连接状态和通信状态：

### 代码实现
1. 功能代码实现放在 `common/parameter-config.js` 文件中。
2. 具体的UI控制代码放在html文件中。
2. 不要影响原有代码和功能，特别是控制按钮。