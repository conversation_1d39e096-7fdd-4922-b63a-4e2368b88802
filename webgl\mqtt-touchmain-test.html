<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TouchMain MQTT 实时数据测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #0a0f1c;
            color: #ffffff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            background: #1a2332;
            border: 1px solid #3a4a5c;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .section h2 {
            color: #00d4ff;
            margin-top: 0;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }
        .status-item {
            background: #2a3441;
            border: 1px solid #3a4a5c;
            border-radius: 6px;
            padding: 10px;
            text-align: center;
        }
        .status-item.active {
            background: #4CAF50;
            border-color: #45a049;
            box-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
        }
        .parameter-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }
        .parameter-item {
            background: #2a3441;
            border: 1px solid #3a4a5c;
            border-radius: 6px;
            padding: 15px;
        }
        .parameter-label {
            font-size: 14px;
            color: #b8c5d6;
            margin-bottom: 5px;
        }
        .parameter-value {
            font-size: 18px;
            font-weight: bold;
            color: #00d4ff;
        }
        .connection-status {
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .connection-status.connected {
            background-color: #4CAF50;
        }
        .connection-status.connecting {
            background-color: #FF9800;
        }
        .connection-status.disconnected {
            background-color: #f44336;
        }
        .debug-info {
            background: #2a3441;
            border: 1px solid #3a4a5c;
            border-radius: 6px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .chart-container {
            height: 300px;
            background: #2a3441;
            border: 1px solid #3a4a5c;
            border-radius: 6px;
            margin-top: 20px;
        }
        button {
            background: #00d4ff;
            color: #0a0f1c;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        button:hover {
            background: #0099cc;
        }
    </style>
    <!-- 引入 ECharts -->
    <script src="./echarts/echarts.min.js"></script>
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入参数配置脚本 -->
    <script src="./common/parameter-config.js"></script>
</head>
<body>
    <div class="container">
        <h1>TouchMain MQTT 实时数据测试页面</h1>
        
        <!-- 连接状态 -->
        <div class="section">
            <h2>连接状态</h2>
            <div id="connection-status" class="connection-status disconnected">
                MQTT 连接状态: 初始化中...
            </div>
            <div>
                <button onclick="testReconnect()">手动重连</button>
                <button onclick="showConnectionInfo()">显示连接信息</button>
                <button onclick="clearDebugInfo()">清除调试信息</button>
            </div>
        </div>

        <!-- 系统状态 -->
        <div class="section">
            <h2>系统运行状态</h2>
            <div class="status-grid">
                <div class="status-item" id="ready-status">
                    <div>就绪</div>
                    <div id="ready-value">0</div>
                </div>
                <div class="status-item" id="running-status">
                    <div>运行</div>
                    <div id="running-value">0</div>
                </div>
                <div class="status-item" id="fault-status">
                    <div>故障</div>
                    <div id="fault-value">0</div>
                </div>
                <div class="status-item" id="standby-status">
                    <div>备用</div>
                    <div id="standby-value">0</div>
                </div>
                <div class="status-item" id="hv-wait-status">
                    <div>合高压等待</div>
                    <div id="hv-wait-value">0</div>
                </div>
            </div>
        </div>

        <!-- 关键参数 -->
        <div class="section">
            <h2>关键参数</h2>
            <div class="parameter-grid">
                <div class="parameter-item">
                    <div class="parameter-label">母线电压Uab:</div>
                    <div class="parameter-value" id="bus-voltage-uab-value">-- kV</div>
                </div>
                <div class="parameter-item">
                    <div class="parameter-label">母线电压Ubc:</div>
                    <div class="parameter-value" id="bus-voltage-ubc-value">-- kV</div>
                </div>
                <div class="parameter-item">
                    <div class="parameter-label">母线电压Uca:</div>
                    <div class="parameter-value" id="bus-voltage-uca-value">-- kV</div>
                </div>
                <div class="parameter-item">
                    <div class="parameter-label">SVG电流Ia:</div>
                    <div class="parameter-value" id="svg-current-ia-value">-- A</div>
                </div>
                <div class="parameter-item">
                    <div class="parameter-label">SVG电流Ib:</div>
                    <div class="parameter-value" id="svg-current-ib-value">-- A</div>
                </div>
                <div class="parameter-item">
                    <div class="parameter-label">SVG电流Ic:</div>
                    <div class="parameter-value" id="svg-current-ic-value">-- A</div>
                </div>
                <div class="parameter-item">
                    <div class="parameter-label">网侧负载无功电流:</div>
                    <div class="parameter-value" id="grid-reactive-current-value">-- A</div>
                </div>
                <div class="parameter-item">
                    <div class="parameter-label">负载无功功率:</div>
                    <div class="parameter-value" id="load-reactive-power-value">-- kVar</div>
                </div>
                <div class="parameter-item">
                    <div class="parameter-label">功率因数:</div>
                    <div class="parameter-value" id="power-factor-value">--</div>
                </div>
            </div>
        </div>

        <!-- 网侧负载无功电流图表 -->
        <div class="section">
            <h2>网侧负载无功电流图表</h2>
            <div id="chart-container" class="chart-container"></div>
        </div>

        <!-- 调试信息 -->
        <div class="section">
            <h2>调试信息</h2>
            <div id="debug-info" class="debug-info">等待调试信息...</div>
        </div>
    </div>

    <script>
        // 全局变量
        let touchMainDataManager = null;
        let testChart = null;
        let debugMessages = [];

        // 模拟 touchLatestElectricalData 全局变量
        window.touchLatestElectricalData = {
            'HMI_32040': { value: null, name: 'SVG电流Ia', unit: 'A' },
            'HMI_32042': { value: null, name: 'SVG电流Ib', unit: 'A' },
            'HMI_32044': { value: null, name: 'SVG电流Ic', unit: 'A' },
            'HMI_32030': { value: null, name: '母线电压Uab', unit: 'kV' },
            'HMI_32032': { value: null, name: '母线电压Ubc', unit: 'kV' },
            'HMI_32034': { value: null, name: '母线电压Uca', unit: 'kV' },
            'HMI_32046': { value: null, name: '网侧负载无功电流', unit: 'A' }
        };

        // 模拟 updateMQTTStatus 函数
        window.updateMQTTStatus = function(status, message) {
            const statusElement = document.getElementById('connection-status');
            if (statusElement) {
                statusElement.className = `connection-status ${status}`;
                statusElement.textContent = `MQTT 连接状态: ${message}`;
            }
            addDebugMessage(`状态更新: ${status} - ${message}`);
        };

        // 模拟 updateTouchDataTimestamp 函数
        window.updateTouchDataTimestamp = function() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN');
            addDebugMessage(`数据时间戳更新: ${timeStr}`);
        };

        // 初始化图表
        function initChart() {
            const chartElement = document.getElementById('chart-container');
            testChart = echarts.init(chartElement);
            
            const option = {
                backgroundColor: 'transparent',
                title: {
                    text: '网侧负载无功电流',
                    textStyle: { color: '#ffffff' }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(26, 31, 46, 0.95)',
                    borderColor: '#ffaa00',
                    textStyle: { color: '#ffffff' }
                },
                xAxis: {
                    type: 'category',
                    data: [],
                    axisLine: { lineStyle: { color: '#ffaa00' } },
                    axisLabel: { color: '#b8c5d6' }
                },
                yAxis: {
                    type: 'value',
                    name: '电流(A)',
                    nameTextStyle: { color: '#ffaa00' },
                    axisLine: { lineStyle: { color: '#ffaa00' } },
                    axisLabel: { color: '#b8c5d6' },
                    splitLine: { lineStyle: { color: 'rgba(255, 170, 0, 0.15)' } }
                },
                series: [{
                    name: '网侧负载无功电流',
                    type: 'line',
                    data: [],
                    smooth: true,
                    lineStyle: { color: '#ffaa00', width: 2 },
                    itemStyle: { color: '#ffaa00' }
                }]
            };
            
            testChart.setOption(option);
        }

        // 更新图表
        window.updateTouchGridCurrentChart = function() {
            if (!testChart) return;
            
            const gridReactiveCurrentValue = window.touchLatestElectricalData['HMI_32046'].value;
            if (gridReactiveCurrentValue === null) return;
            
            const now = new Date();
            const timeStr = now.toLocaleTimeString();
            
            const option = testChart.getOption();
            const xData = option.xAxis[0].data;
            const seriesData = option.series[0].data;
            
            // 保持最多60个数据点
            if (xData.length >= 60) {
                xData.shift();
                seriesData.shift();
            }
            
            xData.push(timeStr);
            seriesData.push(parseFloat(gridReactiveCurrentValue).toFixed(1));
            
            testChart.setOption({
                xAxis: { data: xData },
                series: [{ data: seriesData }]
            });
            
            addDebugMessage(`图表更新: ${gridReactiveCurrentValue}A`);
        };

        // 添加调试信息
        function addDebugMessage(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugMessages.push(`[${timestamp}] ${message}`);
            
            // 保持最多100条消息
            if (debugMessages.length > 100) {
                debugMessages.shift();
            }
            
            const debugElement = document.getElementById('debug-info');
            if (debugElement) {
                debugElement.textContent = debugMessages.join('\n');
                debugElement.scrollTop = debugElement.scrollHeight;
            }
        }

        // 测试重连
        function testReconnect() {
            if (touchMainDataManager) {
                touchMainDataManager.reconnect();
                addDebugMessage('手动重连已触发');
            }
        }

        // 显示连接信息
        function showConnectionInfo() {
            if (touchMainDataManager) {
                const status = touchMainDataManager.getConnectionStatus();
                addDebugMessage(`连接信息: ${JSON.stringify(status, null, 2)}`);
            }
        }

        // 清除调试信息
        function clearDebugInfo() {
            debugMessages = [];
            document.getElementById('debug-info').textContent = '调试信息已清除';
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            addDebugMessage('页面初始化开始');
            
            // 初始化图表
            initChart();
            addDebugMessage('图表初始化完成');
            
            // 初始化 TouchMain 数据管理器
            if (typeof initTouchMainDataManager === 'function') {
                touchMainDataManager = initTouchMainDataManager();
                window.touchMainDataManager = touchMainDataManager;
                addDebugMessage('TouchMain 数据管理器初始化完成');
            } else {
                addDebugMessage('错误: initTouchMainDataManager 函数未找到');
            }
            
            // 定期更新状态
            setInterval(() => {
                if (touchMainDataManager) {
                    const status = touchMainDataManager.getConnectionStatus();
                    if (status.isConnected) {
                        updateMQTTStatus('connected', `已连接 (消息: ${status.messageCount})`);
                    } else {
                        updateMQTTStatus('connecting', `重连中... (${status.reconnectAttempts})`);
                    }
                }
            }, 2000);
            
            addDebugMessage('页面初始化完成');
        });
    </script>
</body>
</html>
