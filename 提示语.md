

 
### 控制按钮集成

在 `run-status-panel` 区域的三个控制按钮已集成 MQTT 功能：

- **启动按钮**: 点击时通过 MQTT 发送启动指令 `{"id": "A1", "value": "0"}`
- **停止按钮**: 点击时通过 MQTT 发送停止指令 `{"id": "A2", "value": "1"}`
- **复位按钮**: 点击时通过 MQTT 发送复位指令 `{"id": "A3", "value": "1"}`

2. 尽量复用 `para` 不要写新的方法。
3.  相关mqtt状态放映在 footer-status-info 中。


2. <!-- 系统运行状态 - 5种状态显示 -->、<!-- 网侧负载无功电流图表 -->、<!-- 系统关键参数 - 2-3列网格布局 --> 调用 main.js MQTTElectricalDataManager 类相关方法刷新数据。
 

 


# todo
优化parameter-config.js中
1. 把MQTT连接相关统一为一个方法

目前有3处 username: 'FastBee',


老password: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjdhM2RjZWY1LTE5ODgtNDg4OS04OTAzLTIwY2I0YjIyZDA0YSJ9.g1HCISIvQd6YkNgWhblKnXqHeRI74lnP1F8qZOd9XV5a7J41Qi77f9jLxxWd_EVN0XJPP1haYeRK3Uz_xrbEuA',


新password: eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImFlZjAzNWRlLWJjM2YtNGY4NS1iZjQzLWVjODgzMmM4ZjNhOCJ9.4uxpZg93ug68gXVwkWVV_kfmMGnpt5LbdabnM4_5DLOf0O9H8Rl17XCM6-2WsSqeMtjnTFf0z1zC9r8eNOaUxA
                    