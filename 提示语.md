

 
### 控制按钮集成

在 `run-status-panel` 区域的三个控制按钮已集成 MQTT 功能：

- **启动按钮**: 点击时通过 MQTT 发送启动指令 `{"id": "A1", "value": "0"}`
- **停止按钮**: 点击时通过 MQTT 发送停止指令 `{"id": "A2", "value": "1"}`
- **复位按钮**: 点击时通过 MQTT 发送复位指令 `{"id": "A3", "value": "1"}`

2. 尽量复用 `para` 不要写新的方法。
3.  相关mqtt状态放映在 footer-status-info 中。


2. <!-- 系统运行状态 - 5种状态显示 -->、<!-- 网侧负载无功电流图表 -->、<!-- 系统关键参数 - 2-3列网格布局 --> 调用 main.js MQTTElectricalDataManager 类相关方法刷新数据。
 

 


# todo
优化parameter-config.js中
1. 把MQTT连接相关统一为一个方法

目前有3处 username: 'FastBee',
