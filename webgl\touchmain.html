<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="logo.png">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入 echarts -->
<!--    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>-->
    <script src="./echarts/echarts.min.js"></script>
    <!-- 引入图表配置文件 -->
    <script src="charts.js"></script>
    <!-- 引入配置文件 -->
    <script src="config.js"></script>
    <script src="../common/main.js"></script>
    <!-- 引入通用参数配置脚本（提供运行状态面板功能） -->
    <script src="../common/parameter-config.js"></script>
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <script>
      /**
       * 初始化运行状态面板：创建设备操作管理器，供 start/stop/reset 按钮调用
       */
      function initRunStatusPanel() {
        if (typeof initDeviceOperationManager === 'function') {
          const manager = initDeviceOperationManager();
          // 初始化 MQTT 连接
          if (manager && typeof manager.initMQTTConnections === 'function') {
            manager.initMQTTConnections();
          }
        }
      }
      document.addEventListener('DOMContentLoaded', initRunStatusPanel);

      /**
       * 启动操作 - 发送 A1 控制信号
       * 复用 parameter-config.js 中的 DeviceOperationManager.startOperation 方法
       */
      function startOperation() {
        if (window.deviceOperationManager && typeof window.deviceOperationManager.startOperation === 'function') {
          window.deviceOperationManager.startOperation();
          updateFooterStatus('启动指令已发送', 'success');
        } else {
          console.error('设备操作管理器未初始化');
          updateFooterStatus('启动失败：设备管理器未初始化', 'error');
        }
      }

      /**
       * 停止操作 - 发送 A2 控制信号
       * 复用 parameter-config.js 中的 DeviceOperationManager.stopOperation 方法
       */
      function stopOperation() {
        if (window.deviceOperationManager && typeof window.deviceOperationManager.stopOperation === 'function') {
          window.deviceOperationManager.stopOperation();
          updateFooterStatus('停止指令已发送', 'success');
        } else {
          console.error('设备操作管理器未初始化');
          updateFooterStatus('停止失败：设备管理器未初始化', 'error');
        }
      }

      /**
       * 复位操作 - 发送 A3 控制信号
       * 复用 parameter-config.js 中的 DeviceOperationManager.resetOperation 方法
       */
      function resetOperation() {
        if (window.deviceOperationManager && typeof window.deviceOperationManager.resetOperation === 'function') {
          window.deviceOperationManager.resetOperation();
          updateFooterStatus('复位指令已发送', 'success');
        } else {
          console.error('设备操作管理器未初始化');
          updateFooterStatus('复位失败：设备管理器未初始化', 'error');
        }
      }

      /**
       * 更新底部状态栏信息
       * @param {string} message - 状态消息
       * @param {string} type - 消息类型：success, error, warning
       */
      function updateFooterStatus(message, type = 'info') {
        const statusElement = document.getElementById('mqtt-operation-status');
        if (statusElement) {
          statusElement.textContent = message;
          statusElement.className = `mqtt-operation-status ${type}`;

          // 3秒后恢复默认状态
          setTimeout(() => {
            statusElement.textContent = 'MQTT 控制就绪';
            statusElement.className = 'mqtt-operation-status ready';
          }, 3000);
        }
      }

      /**
       * 增强的 MQTT 状态更新函数
       * 覆盖 parameter-config.js 中的 updateMQTTStatus 函数以提供更好的状态显示
       * @param {string} status - 连接状态：connected, connecting, disconnected
       * @param {string} message - 状态消息
       */
      function updateMQTTStatus(status, message) {
        const statusElement = document.getElementById('mqtt-status');
        if (statusElement) {
          statusElement.className = `mqtt-connection-status ${status}`;
          statusElement.textContent = message;
        }

        // 同时更新数据时间戳，显示最后的 MQTT 活动时间
        if (status === 'connected') {
          updateTouchDataTimestamp();
        }
      }

      /**
       * 增强的数据时间戳更新函数
       * 显示 MQTT 连接和数据更新的相关信息
       */
      function updateTouchDataTimestamp() {
        const timestampElement = document.getElementById('data-timestamp');
        if (timestampElement) {
          const now = new Date();
          const timeStr = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          });
          timestampElement.textContent = `数据更新时间: ${timeStr}`;
        }
      }
    </script>

    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 网侧负载无功电流图表脚本 -->
    <script>
      // 声明全局图表变量
      var touchGridCurrentChart;

      // 复用 main.html 中的数据存储对象结构
      var touchLatestElectricalData = {
        'HMI_32040': { value: null, name: 'SVG电流la', unit: 'A' }, // SVG电流Ia
        'HMI_32042': { value: null, name: 'SVG电流lb', unit: 'A' }, // SVG电流Ib
        'HMI_32044': { value: null, name: 'SVG电流lc', unit: 'A' }, // SVG电流Ic
        'HMI_32030': { value: null, name: '母线电压Uab', unit: 'kV' }, // 母线电压Uab
        'HMI_32032': { value: null, name: '母线电压Ubc', unit: 'kV' }, // 母线电压Ubc
        'HMI_32034': { value: null, name: '母线电压Uca', unit: 'kV' }, // 母线电压Uca
        'HMI_32046': { value: null, name: '网侧负载无功电流', unit: 'A' } // 网侧负载无功电流
      };

      // 复用 main.html 中的图表配置常量
      const TOUCH_CHART_CONFIG = {
        maxDataPoints: 60 // 图表最大数据点数
      };

      /**
       * 初始化触摸屏网侧负载无功电流图表
       * 复用 main.html 中的 initGridCurrentChart 函数逻辑
       */
      function initTouchGridCurrentChart() {
        console.log('[触摸屏图表初始化] 开始初始化网侧负载无功电流图表');

        const chartElement = document.getElementById('touch-grid-current-chart');
        if (!chartElement) {
          console.warn('[触摸屏图表初始化] 网侧负载无功电流图表容器未找到');
          return;
        }

        touchGridCurrentChart = echarts.init(chartElement);

        // 复用 main.html 中的图表配置
        const option = {
          backgroundColor: 'transparent',
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#ffaa00',
                width: 2
              }
            },
            backgroundColor: 'rgba(26, 31, 46, 0.95)',
            borderColor: '#ffaa00',
            borderWidth: 2,
            borderRadius: 8,
            textStyle: { color: '#ffffff', fontSize: 12 },
            padding: [8, 12],
            position: function (pos, params, el, elRect, size) {
              const obj = { top: 10 };
              obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 30;
              return obj;
            },
            confine: true,
            formatter: function(params) {
              let result = '<div style="font-weight:bold;margin-bottom:5px;">' + params[0].axisValue + '</div>';
              params.forEach(function(item) {
                result += '<div style="display:flex;align-items:center;margin:3px 0;">';
                result += '<span style="display:inline-block;width:10px;height:10px;background-color:' + item.color + ';margin-right:8px;border-radius:50%;"></span>';
                result += '<span style="flex:1;">' + item.seriesName + ': </span>';
                result += '<span style="font-weight:bold;">' + item.value + 'A</span>';
                result += '</div>';
              });
              return result;
            }
          },
          legend: {
            data: ['网侧负载无功电流'],
            textStyle: { color: '#b8c5d6', fontSize: 12 },
            icon: 'circle',
            itemWidth: 10,
            itemHeight: 10,
            itemGap: 15,
            top: 10,
            right: '5%',
            padding: [5, 10],
            backgroundColor: 'rgba(26, 31, 46, 0.3)',
            borderRadius: 4,
            borderColor: 'rgba(255, 170, 0, 0.2)',
            borderWidth: 1
          },
          grid: {
            left: '8%',
            right: '8%',
            bottom: '15%',
            top: '25%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: [],
            axisLine: { lineStyle: { color: '#ffaa00' } },
            axisLabel: { color: '#b8c5d6', fontSize: 10, margin: 10 }
          },
          yAxis: {
            type: 'value',
            name: '电流(A)',
            nameTextStyle: { color: '#ffaa00', fontSize: 10, padding: [0, 0, 5, 0] },
            axisLine: { lineStyle: { color: '#ffaa00' } },
            axisLabel: { color: '#b8c5d6', fontSize: 10 },
            splitLine: { lineStyle: { color: 'rgba(255, 170, 0, 0.15)', type: 'dashed' } }
          },
          series: [
            {
              name: '网侧负载无功电流',
              type: 'line',
              data: [],
              smooth: true,
              symbol: 'circle',
              symbolSize: 6,
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(255, 170, 0, 0.3)' },
                  { offset: 1, color: 'rgba(255, 170, 0, 0.05)' }
                ])
              },
              lineStyle: { color: '#ffaa00', width: 3 },
              itemStyle: {
                color: '#ffaa00',
                borderColor: '#ffffff',
                borderWidth: 1,
                shadowColor: 'rgba(255, 170, 0, 0.5)',
                shadowBlur: 5
              }
            }
          ]
        };
        touchGridCurrentChart.setOption(option);

        console.log('[触摸屏图表初始化] 网侧负载无功电流图表初始化完成');
      }

      /**
       * 更新触摸屏网侧负载无功电流图表（使用真实数据）
       * 复用 main.html 中的 updateGridCurrentChart 函数逻辑
       */
      function updateTouchGridCurrentChart() {
        if (!touchGridCurrentChart) return;

        const now = new Date();
        const timeStr = now.toLocaleTimeString();

        // 从真实数据中获取网侧负载无功电流数据
        const gridReactiveCurrentValue = touchLatestElectricalData['HMI_32046'].value; // 网侧负载无功电流

        // 如果没有真实数据，不更新图表
        if (gridReactiveCurrentValue === null) {
          console.log('[触摸屏网侧负载无功电流图表] 等待真实数据...');
          return;
        }

        const option = touchGridCurrentChart.getOption();
        const xData = option.xAxis[0].data;
        const seriesData = option.series;

        // 保持配置的最大数据点数
        if (xData.length >= TOUCH_CHART_CONFIG.maxDataPoints) {
          xData.shift();
          seriesData[0].data.shift();
        }

        xData.push(timeStr);
        seriesData[0].data.push(parseFloat(gridReactiveCurrentValue).toFixed(1));

        touchGridCurrentChart.setOption({
          xAxis: { data: xData },
          series: seriesData
        });

        console.log(`[触摸屏网侧负载无功电流图表] 更新数据 - 无功电流: ${gridReactiveCurrentValue}A`);
      }



      /**
       * 调整触摸屏图表大小
       * 在窗口大小变化时调用
       */
      function resizeTouchChart() {
        // 添加延迟以确保DOM已完全更新
        setTimeout(function() {
          if (touchGridCurrentChart) {
            touchGridCurrentChart.resize();

            // 重新设置图例位置，确保在调整大小后仍然可见
            let option = touchGridCurrentChart.getOption();
            option.legend.right = '5%';
            touchGridCurrentChart.setOption(option, false);
          }
        }, 200);
      }

      // 监听窗口大小变化事件
      window.addEventListener('resize', resizeTouchChart);

      // 页面加载完成后初始化图表
      window.addEventListener('load', function() {
        // 延迟初始化图表，确保DOM完全加载 - 复用 main.html 中的初始化逻辑
        setTimeout(function() {
          initTouchGridCurrentChart();

          // 初始化图表等待状态
          initTouchChartsWaitingState();


        }, 500);


      });













      /**
       * 初始化触摸屏图表等待数据状态
       * 显示等待数据提示
       */
      function initTouchChartsWaitingState() {
        console.log('[触摸屏图表] 初始化等待状态');

        // 为触摸屏图表添加等待数据的提示
        const chartElement = document.getElementById('touch-grid-current-chart');
        if (chartElement) {
          // 创建等待数据的提示层
          const waitingDiv = document.createElement('div');
          waitingDiv.className = 'touch-chart-waiting-overlay';
          waitingDiv.innerHTML = `
            <div class="touch-waiting-content">
              <i class="fas fa-clock"></i>
              <p>等待数据...</p>
            </div>
          `;
          waitingDiv.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(10, 15, 28, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ff4444;
            font-size: 14px;
            z-index: 10;
          `;

          // 确保父容器有相对定位
          chartElement.parentElement.style.position = 'relative';
          chartElement.parentElement.appendChild(waitingDiv);
        }
      }

      /**
       * 移除触摸屏图表等待状态
       * 复用 main.html 中的 removeChartsWaitingState 函数逻辑
       */
      function removeTouchChartsWaitingState() {
        const waitingOverlays = document.querySelectorAll('.touch-chart-waiting-overlay');
        waitingOverlays.forEach(overlay => {
          overlay.remove();
        });
        console.log('[触摸屏图表] 移除等待状态');
      }

      /**
       * 更新触摸屏数据时间戳
       * 复用 main.html 中的时间戳更新逻辑
       */
      function updateTouchDataTimestamp() {
        const timestampElement = document.getElementById('data-timestamp');
        if (timestampElement) {
          const now = new Date();
          const timeStr = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          });
          timestampElement.textContent = `数据更新时间: ${timeStr}`;
        }
      }
    </script>

    <style>
      /**
       * 桂林智源 Unity WebGL 界面切换功能样式
       * 实现Unity 3D界面和电气拓扑图之间的切换功能
       */

      /* Unity包装容器 */
      .unity-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      /* 侧边箭头按钮 - 优化为低调设计 */
      .arrow-btn {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 50px;
        height: 70px;
        /* 默认状态：几乎透明的背景，无边框，无阴影 */
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        color: rgba(255, 255, 255, 0.4);
        font-size: 20px;
        cursor: pointer;
        transition: all 0.4s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        /* 移除默认的backdrop-filter和box-shadow */
      }

      .arrow-btn:hover {
        /* 悬停时显示完整的视觉效果 */
        background: linear-gradient(135deg, rgba(26, 35, 50, 0.95), rgba(42, 52, 65, 0.95));
        border: 2px solid #00d4ff;
        color: #00d4ff;
        backdrop-filter: blur(10px);
        box-shadow: 0 6px 25px rgba(0, 212, 255, 0.6);
        transform: translateY(-50%) scale(1.1);
      }

      /* 触摸屏图表等待状态样式 */
      .touch-chart-waiting-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(10, 15, 28, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ff4444;
        font-size: 14px;
        z-index: 10;
        border-radius: 8px;
      }

      .touch-waiting-content {
        text-align: center;
        padding: 20px;
        background: rgba(26, 31, 46, 0.9);
        border-radius: 8px;
        border: 1px solid rgba(255, 68, 68, 0.3);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
      }

      .touch-waiting-content i {
        font-size: 24px;
        margin-bottom: 10px;
        color: #ff4444;
      }

      .touch-waiting-content p {
        margin: 0;
        font-size: 14px;
        font-weight: bold;
        color: #ff4444;
      }

      .arrow-btn:active {
        background: linear-gradient(135deg, #00d4ff, #0099cc);
        color: #0a0f1c;
        transform: translateY(-50%) scale(0.95);
        box-shadow: 0 2px 15px rgba(0, 212, 255, 0.8);
      }

      /* 左侧箭头按钮 */
      .arrow-btn.left {
        left: 15px;
        border-radius: 12px 4px 4px 12px;
      }

      /* 右侧箭头按钮 */
      .arrow-btn.right {
        right: 15px;
        border-radius: 4px 12px 12px 4px;
      }

      /* 箭头按钮图标动画 */
      .arrow-btn i {
        transition: transform 0.3s ease;
      }

      .arrow-btn:hover i {
        transform: scale(1.2);
      }

      /* 当前视图状态指示 - 优化为低调设计 */
      .arrow-btn.current-view {
        /* 默认状态：稍微明显一点但仍然低调 */
        background: rgba(0, 255, 136, 0.1);
        border: 1px solid rgba(0, 255, 136, 0.3);
        color: rgba(0, 255, 136, 0.6);
        /* 移除默认的box-shadow */
      }

      /* 数据状态指示器样式 */
      .data-quality-indicator {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
        color: white;
        display: inline-block;
        margin: 2px;
      }

      .data-quality-indicator.excellent {
        background-color: #4CAF50;
      }

      .data-quality-indicator.good {
        background-color: #8BC34A;
      }

      .data-quality-indicator.fair {
        background-color: #FF9800;
      }

      .data-quality-indicator.poor {
        background-color: #f44336;
      }

      /* MQTT 连接状态指示器样式 */
      .mqtt-connection-status {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
        color: white;
        display: inline-block;
        margin: 2px;
        transition: all 0.3s ease;
      }

      .mqtt-connection-status.connected {
        background-color: #4CAF50;
        box-shadow: 0 0 8px rgba(76, 175, 80, 0.3);
      }

      .mqtt-connection-status.connecting {
        background-color: #FF9800;
        animation: pulse 1.5s infinite;
      }

      .mqtt-connection-status.disconnected {
        background-color: #f44336;
        animation: pulse 1s infinite;
      }

      /* MQTT 控制操作状态指示器样式 */
      .mqtt-operation-status {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
        color: white;
        display: inline-block;
        margin: 2px;
        transition: all 0.3s ease;
      }

      .mqtt-operation-status.ready {
        background-color: #2196F3;
      }

      .mqtt-operation-status.success {
        background-color: #4CAF50;
        box-shadow: 0 0 8px rgba(76, 175, 80, 0.3);
      }

      .mqtt-operation-status.error {
        background-color: #f44336;
        animation: pulse 1s infinite;
      }

      .mqtt-operation-status.warning {
        background-color: #FF9800;
      }





      .system-status {
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: bold;
        color: white;
        display: inline-block;
        margin: 4px;
      }

      .system-status.running {
        background-color: #4CAF50;
      }

      .system-status.fault {
        background-color: #f44336;
        animation: pulse 1s infinite;
      }

      .system-status.ready {
        background-color: #2196F3;
      }

      .system-status.standby {
        background-color: #9E9E9E;
      }

      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
      }



      .arrow-btn.current-view:hover {
        /* 悬停时显示完整的当前视图效果 */
        background: linear-gradient(135deg, #00ff88, #00d4ff);
        border: 2px solid #00ff88;
        color: #0a0f1c;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 20px rgba(0, 255, 136, 0.4);
        transform: translateY(-50%) scale(1.05);
      }

      /* Unity容器样式调整 */
      .unity-container {
        position: absolute;
        width: 100%;
        height: 100%;
        transition: opacity 0.5s ease;
        opacity: 1;
        visibility: visible;
      }

      /* 电气拓扑iframe容器 */
      .topology-container {
        position: absolute;
        width: 100%;
        height: 100%;
        opacity: 0;
        visibility: hidden;
        transition: all 0.5s ease;
      }

      .topology-iframe {
        width: 100%;
        height: 100%;
        border: none;
        background: #0a0f1c;
      }

      /* 显示状态控制 - 支持多页面循环切换 */
      .unity-wrapper .unity-container,
      .unity-wrapper .topology-container {
        opacity: 0;
        visibility: hidden;
      }

      /* Unity 3D界面 */
      .unity-wrapper.show-unity .unity-container {
        opacity: 1;
        visibility: visible;
      }

      /* 电气拓扑界面 */
      .unity-wrapper.show-electrical-topology .electrical-topology-container {
        opacity: 1;
        visibility: visible;
      }

      /* 水冷拓扑界面 */
      .unity-wrapper.show-cooling-topology .cooling-topology-container {
        opacity: 1;
        visibility: visible;
      }

      /* 加载动画覆盖层 */
      .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(10, 15, 28, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 20px;
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }

      .loading-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #3a4a5c;
        border-top: 3px solid #00d4ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .loading-text {
        color: #b8c5d6;
        font-size: 14px;
        font-family: 'Microsoft YaHei', sans-serif;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* 响应式设计 */
      @media (max-width: 1366px) {
        .arrow-btn {
          width: 45px;
          height: 60px;
          font-size: 18px;
        }

        .arrow-btn.left {
          left: 12px;
        }

        .arrow-btn.right {
          right: 12px;
        }
      }

      @media (max-width: 768px) {
        .arrow-btn {
          width: 40px;
          height: 55px;
          font-size: 16px;
        }

        .arrow-btn.left {
          left: 8px;
        }

        .arrow-btn.right {
          right: 8px;
        }
      }

      /* 主内容区域 - 侧边栏、左栏、中栏三栏布局 */
      .main-content {
          grid-template-columns: 120px 540px 1fr;
      }

      /* 响应式设计 - 适应三栏布局 */
      /* 1080p显示器优化 */
    @media (max-width: 1920px) and (max-height: 1080px) {
      .main-content {
        grid-template-columns: 120px 540px 1fr;
      }
    }

    @media (max-width: 1600px) {
      .main-content {
          grid-template-columns: 120px 540px 1fr;
      }
    }

    @media (max-width: 1400px) {
        .main-content {
            grid-template-columns: 120px 540px 1fr;
        }
    }

      /* 登录功能样式 */
      .login-info {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .login-btn {
        background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
        border: 2px solid var(--border-color);
        border-radius: 12px;
        color: var(--text-primary);
        cursor: pointer;
        padding: 10px 20px;
        font-size: 16px;
        font-weight: bold;
        transition: all 0.3s ease;
        touch-action: manipulation;
      }

      .login-btn:hover,
      .login-btn:active {
        border-color: var(--primary-color);
        box-shadow: 0 6px 25px rgba(0, 212, 255, 0.4);
        transform: scale(1.05);
      }

      /* 左侧导航栏样式 */
      .sidebar {
        background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
        border-right: 2px solid var(--border-color);
        padding: 10px 5px;
        overflow-y: auto;
        /* 隐藏滚动条但保持滚动功能 */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
      }

      /* 隐藏滚动条 - Webkit 浏览器 (Chrome, Safari) */
      .sidebar::-webkit-scrollbar {
        display: none;
      }

      .nav-menu {
        display: flex;
        flex-direction: column;
        gap: 0;
        background: rgba(26, 35, 50, 0.5);
        border-radius: 8px;
        overflow: hidden;
      }

      .nav-item {
        background: transparent;
        border: none;
        border-bottom: 1px solid rgba(58, 74, 92, 0.5);
        border-radius: 0;
        color: var(--text-primary);
        cursor: pointer;
        padding: 12px 8px;
        font-size: 14px;
        font-weight: bold;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 8px;
        touch-action: manipulation;
        min-height: 70px;
        text-align: center;
        position: relative;
      }

      /* 移除最后一个导航项的下边框 */
      .nav-item:last-child {
        border-bottom: none;
      }

      .nav-item:hover,
      .nav-item:active {
        background: linear-gradient(90deg, rgba(0, 212, 255, 0.2), transparent);
        border-left: 3px solid var(--primary-color);
        box-shadow: inset 0 0 15px rgba(0, 212, 255, 0.2);
        transform: none;
      }

      /* 添加活动状态样式 */
      .nav-item.active {
        background: linear-gradient(90deg, rgba(0, 212, 255, 0.3), transparent);
        border-left: 3px solid var(--primary-color);
        box-shadow: inset 0 0 15px rgba(0, 212, 255, 0.3);
      }

      .nav-item i {
        font-size: 24px;
        color: var(--primary-color);
        text-shadow: 0 2px 8px rgba(0, 212, 255, 0.5);
        margin-bottom: 2px;
      }

      .nav-item span {
        font-size: 12px;
        line-height: 1.2;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
      }

      .nav-item.hidden {
        display: none;
      }

      .nav-item.hidden-by-permission {
        display: none !important;
      }

      /* 弹窗样式 */
      .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }

      .modal-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      .modal-container {
        width: 100vw;
        height: 100vh;
        background: var(--bg-primary);
        border: none;
        border-radius: 0;
        position: relative;
        overflow: hidden;
        box-shadow: none;
      }

      .modal-header {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        z-index: 1001;
        background: transparent;
      }

      .modal-header-left {
        display: flex;
        align-items: center;
        gap: 15px;
      }



      .debug-menu-btn {
        background: var(--bg-tertiary);
        border: 2px solid var(--border-color);
        border-radius: 8px;
        color: var(--primary-color);
        cursor: pointer;
        padding: 8px 12px;
        font-size: 16px;
        transition: all 0.3s ease;
        touch-action: manipulation;
      }

      .debug-menu-btn:hover,
      .debug-menu-btn:active {
        border-color: var(--primary-color);
        box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
        transform: scale(1.05);
      }

      .modal-close-btn {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, rgba(42, 52, 65, 0.9), rgba(26, 35, 50, 0.9));
        border: 2px solid var(--border-color);
        border-radius: 12px;
        color: var(--text-primary);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        transition: all 0.3s ease;
        touch-action: manipulation;
      }

      .modal-close-btn:hover,
      .modal-close-btn:active {
        background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(0, 153, 204, 0.2));
        border-color: var(--primary-color);
        color: var(--primary-color);
        box-shadow: 0 6px 25px rgba(0, 212, 255, 0.4);
        transform: scale(1.05);
      }

      .modal-content {
        width: 100%;
        height: 100%;
        position: relative;
      }

      .modal-iframe {
        width: 100%;
        height: 100%;
        border: none;
        background: var(--bg-primary);
      }

      /* 登录弹窗样式 */
      .login-modal {
        width: 400px;
        max-width: 90vw;
        height: auto;
        min-height: 300px;
        background: var(--bg-secondary);
        border: 2px solid var(--primary-color);
        border-radius: 15px;
        padding: 25px;
        display: flex;
        flex-direction: column;
        gap: 20px;
        box-sizing: border-box;
      }

      .login-form {
        display: flex;
        flex-direction: column;
        gap: 15px;
        width: 100%;
        box-sizing: border-box;
      }

      .form-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
        width: 100%;
      }

      .form-group label {
        font-size: 16px;
        color: var(--text-primary);
        font-weight: bold;
      }

      .form-group input {
        padding: 12px 15px;
        border: 2px solid var(--border-color);
        border-radius: 8px;
        background: var(--bg-tertiary);
        color: var(--text-primary);
        font-size: 16px;
        transition: all 0.3s ease;
        width: 100%;
        box-sizing: border-box;
        touch-action: manipulation;
      }

      .form-group input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
      }

      .login-buttons {
        display: flex;
        gap: 15px;
        justify-content: center;
        width: 100%;
        margin-top: 10px;
        box-sizing: border-box;
      }

      .btn {
        padding: 12px 20px;
        border: 2px solid var(--border-color);
        border-radius: 8px;
        background: var(--bg-tertiary);
        color: var(--text-primary);
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        touch-action: manipulation;
        flex: 1;
        max-width: 150px;
        box-sizing: border-box;
      }

      .btn.primary {
        border-color: var(--primary-color);
        background: var(--primary-color);
        color: var(--bg-primary);
      }

      .btn:hover,
      .btn:active {
        transform: scale(1.05);
        box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
      }

      /* 调试菜单样式 */
      .debug-menu {
        position: absolute;
        top: 80px;
        left: 20px;
        width: 300px;
        max-height: calc(100% - 100px);
        background: var(--bg-secondary);
        border: 2px solid var(--primary-color);
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
        z-index: 1002;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
      }

      .debug-menu.show {
        opacity: 1;
        transform: translateY(0);
      }

      .debug-menu-content {
        display: flex;
        flex-direction: column;
        height: 100%;
      }



      .debug-menu-list {
        flex: 1;
        overflow-y: auto;
        padding: 10px;
      }

      .debug-menu-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 15px;
        margin-bottom: 5px;
        background: var(--bg-tertiary);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        color: var(--text-primary);
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        touch-action: manipulation;
      }

      .debug-menu-item:hover,
      .debug-menu-item:active {
        border-color: var(--primary-color);
        box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
        transform: translateX(5px);
      }

      .debug-menu-item.active {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-color: var(--primary-color);
        color: var(--bg-primary);
      }

      .debug-menu-item i {
        color: var(--primary-color);
        font-size: 14px;
        width: 16px;
        text-align: center;
      }

      .debug-menu-item.active i {
        color: var(--bg-primary);
      }

      /* 权限配置弹窗样式 */
      .permission-modal {
        width: 500px;
        height: 600px;
        background: var(--bg-secondary);
        border: 2px solid var(--primary-color);
        border-radius: 15px;
        padding: 30px;
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .permission-list {
        flex: 1;
        overflow-y: auto;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
      }

      .permission-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px;
        background: var(--bg-tertiary);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        min-height: 60px;
        box-sizing: border-box;
      }

      .permission-label {
        font-size: 16px;
        color: var(--text-primary);
      }

      .toggle-switch {
        position: relative;
        width: 60px;
        height: 30px;
        background: var(--border-color);
        border-radius: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .toggle-switch.active {
        background: var(--primary-color);
      }

      .toggle-switch::after {
        content: '';
        position: absolute;
        top: 3px;
        left: 3px;
        width: 24px;
        height: 24px;
        background: white;
        border-radius: 50%;
        transition: all 0.3s ease;
      }

      .toggle-switch.active::after {
        left: 33px;
      }

      /* CSS变量定义 */
      :root {
        --primary-color: #00d4ff;
        --secondary-color: #0099cc;
        --accent-color: #00ff88;
        --warning-color: #ffaa00;
        --error-color: #ff4444;
        --success-color: #00ff88;
        --bg-primary: #0a0f1c;
        --bg-secondary: #1a2332;
        --bg-tertiary: #2a3441;
        --text-primary: #ffffff;
        --text-secondary: #b8c5d6;
        --border-color: #3a4a5c;
        --shadow-color: rgba(0, 212, 255, 0.3);
      }

      /* 图表区域样式 */
      .chart-section {
        background: linear-gradient(135deg, rgba(26, 35, 50, 0.8), rgba(42, 52, 65, 0.8));
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 10px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      }

      /* 左侧面板字体放大样式 - 针对触摸屏版本 */
      .left-panel {
        font-size: 16px; /* 放大基础字体大小 */
      }
      
      .left-panel .panel-header h3 {
        font-size: 18px; /* 放大面板标题字体 */
      }
      
      .left-panel .status-section h4 {
        font-size: 16px; /* 放大状态区域标题字体 */
      }
      
      .left-panel .status-label {
        font-size: 14px; /* 放大状态标签字体 */
      }
      
      .left-panel .parameter-label {
        font-size: 14px; /* 放大参数标签字体 */
      }
      
      .left-panel .parameter-value {
        font-size: 15px; /* 放大参数值字体 */
      }
      
      .left-panel .chart-header h4 {
        font-size: 16px; /* 放大图表标题字体 */
      }
      
      .left-panel .control-button {
        font-size: 14px !important; /* 调整控制按钮字体大小与status-label保持一致 */
      }
      
      .left-panel .control-button i {
        font-size: 16px !important; /* 放大控制按钮图标大小 */
      }

      .chart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        padding-bottom: 8px;
        border-bottom: 1px solid rgba(58, 74, 92, 0.5);
      }

      .chart-header h4 {
        color: var(--text-primary);
        font-size: 14px;
        font-weight: bold;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .chart-header i {
        color: var(--primary-color);
        font-size: 16px;
      }

      .chart-container {
        position: relative;
        width: 100%;
        height: 200px;
        background: rgba(10, 15, 28, 0.5);
        border-radius: 6px;
        overflow: hidden;
      }

      .chart-content {
        width: 100%;
        height: 100%;
      }

      /* 响应式设计 */
      @media (max-width: 1920px) {
        body {
          width: 100vw;
          height: 100vh;
        }

        .modal-container {
          width: 100vw;
          height: 100vh;
          border-radius: 0;
        }

        .permission-modal {
          width: 90vw;
          max-width: 800px;
          height: auto;
          max-height: 80vh;
        }

        .permission-list {
          grid-template-columns: repeat(2, 1fr);
          gap: 15px;
        }
      }

      @media (max-width: 768px) {
        .permission-list {
          grid-template-columns: 1fr;
        }
      }

      /* 运行状态面板（垂直布局，作用域限定，60%缩放，无背景框） */
      #run-status-panel.protection-panel {
        background: transparent; /* 移除背景框 */
        border: none; /* 移除边框 */
        border-radius: 0; /* 移除圆角 */
        padding: 4.8px 7.2px; /* 保持内边距确保布局不受影响 */
        margin-top: 2px; /* 与上方 status-section 更紧凑衔接 */
        margin-left: auto; /* 居中显示 */
        margin-right: auto; /* 居中显示 */
        width: 80%; /* 设置为 status-section 宽度的 80% */
        max-width: 80%; /* 确保不超过 80% 宽度 */
        overflow: visible;
        min-width: 0;
        margin-bottom: 10px;
      }
      /* 三个操作项水平排列 */
      #run-status-panel .run-status-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 4px; /* 进一步收紧操作项间距，适配缩小的面板宽度 */
        align-items: stretch;
      }
      /* 单个操作项内部垂直布局：状态灯在上，按钮在下 */
      #run-status-panel .run-status-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 6px; /* 由 10px 等比缩放至 60% */
        padding: 2.4px 0; /* 由 4px 等比缩放至 60% */
      }
      /* 顶部状态指示器（胶囊形） */
      #run-status-panel .status-indicator {
        width: 72px;  /* 由 120px 等比缩放至 60% */
        height: 6px;  /* 由 10px 等比缩放至 60% */
        border-radius: 4.8px; /* 由 8px 等比缩放至 60% */
        background: #6b7480;
        border: 1px solid rgba(0, 0, 0, 0.3);
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
        transition: all 0.25s ease;
        margin: 0;
      }
      #run-status-panel .status-indicator.active {
        background: #4CAF50;
        border-color: #45a049;
        box-shadow: 0 0 8px rgba(76, 175, 80, 0.5), inset 0 2px 4px rgba(0, 0, 0, 0.2);
      }
      #run-status-panel .status-indicator.stop {
        background: #f44336;
        border-color: #da190b;
        box-shadow: 0 0 8px rgba(244, 67, 54, 0.5), inset 0 2px 4px rgba(0, 0, 0, 0.2);
      }
      /* 按钮区域 */
      #run-status-panel .control-button {
        width: 108px;  /* 由 72px 增加 50% (72px × 1.5 = 108px) */
        height: 27px;  /* 由 45px 等比缩放至 60% */
        border: 1px solid rgba(0, 212, 255, 0.2);
        border-radius: 6px; /* 由 10px 等比缩放至 60% */
        background: rgba(42, 49, 66, 0.85);
        font-size: 9.6px; /* 由 16px 等比缩放至 60% */
        font-weight: bold;
        color: #fff;
        cursor: pointer;
        transition: transform 0.15s ease, box-shadow 0.15s ease, background-color 0.15s ease, filter 0.15s ease; /* 点击与悬停动画过渡 */
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4.8px; /* 由 8px 等比缩放至 60% */
        position: relative; /* 为 ::before 顶部高亮条做定位 */
        overflow: hidden;
      }
      #run-status-panel .control-button i { font-size: 9.6px; }
      /* 为三种按钮提供直观底色（设备操作激活时仍会被内联样式加深） */
      #run-status-panel .control-button.start-button { background: #3ea85b; }
      #run-status-panel .control-button.stop-button  { background: #cf3d34; }
      #run-status-panel .control-button.reset-button { background: #3c6de9; }
      /* 参照 .status-item 的顶端渐变条效果 */
      #run-status-panel .control-button::before {
        content: '';
        position: absolute;
        top: 0; left: 0;
        width: 100%; height: 2px;
        background: transparent;
        transition: all 0.15s ease;
      }
      /* 悬停效果增强：与 .status-item 类似的悬停视觉 */
      #run-status-panel .control-button:hover {
        transform: translateY(-2px);
        border-color: rgba(0, 212, 255, 0.4);
        box-shadow: 0 4px 12px rgba(0, 212, 255, 0.25);
        filter: brightness(1.06);
      }
      #run-status-panel .control-button:hover::before {
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
      }
      /* 点击时缩放，兼容 hover（合并 translate 与 scale） */
      #run-status-panel .control-button:active {
        transform: translateY(-1px) scale(0.96);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
        filter: brightness(0.98);
      }

    </style>
  </head>
  <body>
    <div id="main-container" class="app-container">
      <!-- 顶部导航栏 -->
      <header class="header">
        <!-- 区域1：项目标识 -->
        <div class="header-left">
          <div class="logo">
            <img src="logo.png" alt="桂林智源" class="logo-image">
            <span class="logo-text">桂林智源</span>
          </div>
          <div class="system-title">SVG 数字化系统</div>
        </div>

        <!-- 区域2：系统标题和菜单 -->
        <div class="header-center">
          <div class="system-title-section">
            <h1 class="main-title">中科院等离子极向场无功补偿SVG-B3</h1>
          </div>
        </div>

        <!-- 区域3：系统信息 -->
        <div class="header-right">
          <div class="system-info-panel">
            <!-- 登录功能组件 -->
            <div class="login-info">
              <button class="login-btn" id="loginBtn" onclick="toggleLogin()">
                <i class="fas fa-user"></i>
                <span id="loginText">登录</span>
              </button>
            </div>
            <div class="time-display" id="currentTime"></div>
          </div>
        </div>
      </header>

      <!-- 主内容区域 -->
      <main class="main-content">
        <!-- 左侧导航栏 -->
        <aside class="sidebar">
          <nav class="nav-menu">
            <div class="nav-item" onclick="openModule('electrical-topology')">
              <i class="fas fa-bolt"></i>
              <span>电气拓扑</span>
            </div>
            <div class="nav-item" onclick="openModule('unit-status')">
              <i class="fas fa-microchip"></i>
              <span>单元状态</span>
            </div>
            <div class="nav-item" onclick="openModule('history-event')">
              <i class="fas fa-calendar-alt"></i>
              <span>历史事件</span>
            </div>
            <div class="nav-item hidden-by-permission" onclick="openModule('parameter-curve')">
              <i class="fas fa-chart-area"></i>
              <span>参数曲线</span>
            </div>
            <div class="nav-item hidden-by-permission" onclick="openModule('fault-wave')">
              <i class="fas fa-wave-square"></i>
              <span>故障录波</span>
            </div>
            <div class="nav-item hidden-by-permission" onclick="openModule('dsp')">
              <i class="fas fa-microchip"></i>
              <span>DSP参数</span>
            </div>
            <div class="nav-item hidden-by-permission" onclick="openModule('io-status')">
              <i class="fas fa-plug"></i>
              <span>IO状态</span>
            </div>
            <div class="nav-item hidden-by-permission" onclick="openModule('cooling-topology')">
              <i class="fas fa-tint"></i>
              <span>水冷系统</span>
            </div>
            <div class="nav-item hidden-by-permission" onclick="showDebugMenu('debug1')">
              <i class="fas fa-cogs"></i>
              <span>调试参数</span>
            </div>
            <div class="nav-item hidden" id="permissionItem" onclick="openPermissionConfig()">
              <i class="fas fa-key"></i>
              <span>权限</span>
            </div>
            <div class="nav-item hidden" id="debug2Item" onclick="showDebugMenu('debug2')">
              <i class="fas fa-tools"></i>
              <span>调试参数2</span>
            </div>
            <div class="nav-item hidden-by-permission" onclick="openModule('version-info')">
              <i class="fas fa-info-circle"></i>
              <span>版本信息</span>
            </div>
          </nav>
        </aside>

        <!-- 区域4：左栏 - 系统状态与参数 -->
        <aside class="left-panel">
          <!-- 电气系统区域-->
          <div class="system-status-area">
            <div class="panel-header">
              <h3><i class="fas fa-bolt"></i>电气系统</h3>
            </div>

            <!-- 系统运行状态 - 5种状态显示 -->
            <div class="status-section">
              <h4><i class="fas fa-power-off"></i>系统状态</h4>
              <div class="status-grid-five">
                <div class="status-item" data-status="ready" id="ready-status">
                  <div class="status-indicator ready">
                    <i class="fas fa-circle"></i>
                  </div>
                  <span class="status-label">就绪</span>
                </div>

                <div class="status-item" data-status="fault" id="fault-status">
                  <div class="status-indicator fault">
                    <i class="fas fa-circle"></i>
                  </div>
                  <span class="status-label">故障</span>
                </div>

                <div class="status-item" data-status="standby" id="standby-status">
                  <div class="status-indicator standby">
                    <i class="fas fa-circle"></i>
                  </div>
                  <span class="status-label">备用</span>
                </div>

                <div class="status-item" data-status="hv-wait" id="hv-wait-status">
                  <div class="status-indicator waiting">
                    <i class="fas fa-circle"></i>
                  </div>
                  <span class="status-label">合高压等待</span>
                </div>

                <div class="status-item active" data-status="running" id="running-status">
                  <div class="status-indicator running">
                    <i class="fas fa-circle"></i>
                  </div>
                  <span class="status-label">运行</span>
                </div>
              </div>
            </div>

	            <!-- 运行状态面板（垂直布局：状态在上，按钮在下；三个操作项水平排列） -->
	            <div id="run-status-panel" class="protection-panel">
	              <div class="run-status-grid">
	                <div class="run-status-item">
	                  <div class="status-indicator" id="start-indicator"></div>
	                  <button class="control-button start-button" onclick="startOperation()">
	                    <i class="fas fa-play"></i>
	                    启动
	                  </button>
	                </div>
	                <div class="run-status-item">
	                  <div class="status-indicator stop" id="stop-indicator"></div>
	                  <button class="control-button stop-button" onclick="stopOperation()">
	                    <i class="fas fa-stop"></i>
	                    停止
	                  </button>
	                </div>
	                <div class="run-status-item">
	                  <div class="status-indicator" id="reset-indicator"></div>
	                  <button class="control-button reset-button" onclick="resetOperation()">
	                    <i class="fas fa-sync-alt"></i>
	                    复位
	                  </button>
	                </div>
	              </div>
	            </div>


            <!-- 网侧负载无功电流图表 -->
            <div class="chart-section">
              <div class="chart-header">
                <h4><i class="fas fa-wave-square"></i>网侧负载无功电流</h4>
              </div>
              <div class="chart-container" id="touch-grid-current-chart-container">
                <div id="touch-grid-current-chart" class="chart-content"></div>
              </div>
            </div>

            <!-- 系统关键参数 - 2-3列网格布局 -->
            <div class="parameters-section">
              <h4><i class="fas fa-chart-bar"></i>关键参数</h4>
              <div class="parameter-grid-compact">
                <div class="parameter-item" onclick="showParameterDetails('load-reactive-power')">
                  <div class="parameter-content">
                    <div class="parameter-label">负载无功功率:</div>
                    <div class="parameter-value" id="load-reactive-power-value">-2.35 MVAr</div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('power-factor')">
                  <div class="parameter-content">
                    <div class="parameter-label">功率因数:</div>
                    <div class="parameter-value" id="power-factor-value">0.95</div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('grid-reactive-current')">
                  <div class="parameter-content">
                    <div class="parameter-label">网侧负载无功电流:</div>
                    <div class="parameter-value" id="grid-reactive-current-value">125.8 A</div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('bus-voltage-uab')">
                  <div class="parameter-content">
                    <div class="parameter-label">母线电压Uab:</div>
                    <div class="parameter-value" id="bus-voltage-uab-value">10.52 kV</div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('bus-voltage-ubc')">
                  <div class="parameter-content">
                    <div class="parameter-label">母线电压Ubc:</div>
                    <div class="parameter-value" id="bus-voltage-ubc-value">10.48 kV</div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('bus-voltage-uca')">
                  <div class="parameter-content">
                    <div class="parameter-label">母线电压Uca:</div>
                    <div class="parameter-value" id="bus-voltage-uca-value">10.51 kV</div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('svg-current-ia')">
                  <div class="parameter-content">
                    <div class="parameter-label">SVG电流Ia:</div>
                    <div class="parameter-value" id="svg-current-ia-value">128.5 A</div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('svg-current-ib')">
                  <div class="parameter-content">
                    <div class="parameter-label">SVG电流Ib:</div>
                    <div class="parameter-value" id="svg-current-ib-value">126.2 A</div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('svg-current-ic')">
                  <div class="parameter-content">
                    <div class="parameter-label">SVG电流Ic:</div>
                    <div class="parameter-value" id="svg-current-ic-value">127.8 A</div>
                  </div>
                </div>
              </div>
            </div>
          </div>



        </aside>

        <!-- 区域5：中栏 - 3D模型与故障信息 -->
        <section class="center-panel">
          <!-- Unity WebGL 界面切换容器 -->
          <div class="unity-wrapper" id="unityWrapper">
            <!-- 左侧箭头按钮 - 切换到上一个页面 -->
            <button class="arrow-btn left" id="prev-arrow" onclick="switchToPreviousView()" title="切换到上一个页面">
              <i class="fas fa-chevron-left"></i>
            </button>

            <!-- 右侧箭头按钮 - 切换到下一个页面 -->
            <button class="arrow-btn right" id="next-arrow" onclick="switchToNextView()" title="切换到下一个页面">
              <i class="fas fa-chevron-right"></i>
            </button>

            <!-- Unity 3D容器 -->
            <div class="unity-container show-unity" id="unityContainer">
              <iframe id="unity-iframe" src="index.html" frameborder="0" class="unity-iframe"></iframe>
              <div class="unity-placeholder" id="unityPlaceholder" style="display: none;">
                <div class="placeholder-content">
                  <i class="fas fa-cube rotating"></i>
                  <h3>3D模型加载中...</h3>
                  <p>正在初始化Unity WebGL引擎</p>
                  <div class="loading-bar">
                    <div class="loading-progress" id="loadingProgress"></div>
                  </div>
                </div>
              </div>

              <!-- 3D场景控制工具栏 - 移动到Unity容器内部 -->
              <div class="scene-toolbar">
                <div class="toolbar-group">
                  <button class="toolbar-btn" id="reset-view-btn" title="重置视角" onclick="resetUnityView()">
                    <i class="fas fa-home"></i>
                    <span></span>
                  </button>
                </div>
              </div>
            </div>

            <!-- 电气拓扑容器 -->
            <div class="topology-container electrical-topology-container" id="electricalTopologyContainer">
              <iframe id="electrical-topology-iframe" src="" frameborder="0" class="topology-iframe"></iframe>
            </div>

            <!-- 水冷拓扑容器 -->
            <div class="topology-container cooling-topology-container" id="coolingTopologyContainer">
              <iframe id="cooling-topology-iframe" src="" frameborder="0" class="topology-iframe"></iframe>
            </div>

            <!-- 加载动画覆盖层 -->
            <div class="loading-overlay" id="loadingOverlay">
              <div class="loading-spinner"></div>
              <div class="loading-text">正在加载页面...</div>
            </div>
          </div>

          <!-- 实时报警监控展示 -->
          <div class="alarm-monitor-section">
            <div class="alarm-header">
              <h4><i class="fas fa-bell"></i>实时报警监控</h4>
              <div class="alarm-filters">
                <button class="filter-btn active" data-filter="all">所有事件</button>
                <button class="filter-btn" data-filter="alarm">报警事件</button>
                <button class="filter-btn" data-filter="fault">故障事件</button>
              </div>
              <div class="alarm-status">
                <span class="data-status" id="dataStatus">正在加载...</span>
                <span class="last-update" id="lastUpdate"></span>
              </div>
            </div>
            <div class="alarm-list" id="alarmList">
              <!-- 报警监控信息将通过JavaScript动态生成 -->
            </div>
          </div>
        </section>


      </main>

      <!-- 底部状态栏 -->
      <footer class="footer">
        <div class="footer-left">
          <div class="system-info">
            <span>系统版本: v2.1.0</span>
            <span>|</span>
            <span>最后更新: 2025-06-24 14:30:25</span>
          </div>
        </div>
        <div class="footer-center">
          <!-- 合并为单行显示的数据时间戳和状态指示器 -->
          <div class="footer-status-info single-line">
            <div class="data-timestamp" id="data-timestamp">数据更新时间: 2025/07/24 13:04:29</div>
            <div class="status-indicators">
              <!-- MQTT 连接状态指示器 -->
              <div id="mqtt-status" class="mqtt-connection-status disconnected">MQTT 连接中...</div>
              <!-- MQTT 控制操作状态指示器 -->
              <div id="mqtt-operation-status" class="mqtt-operation-status ready">MQTT 控制就绪</div>
              <div id="data-quality-indicator" class="data-quality-indicator excellent">数据质量: 100.0%</div>
            </div>
          </div>
        </div>
        <div class="footer-right">
          <div class="copyright">
            © 2025 桂林智源 - SVG 数字化系统
          </div>
        </div>
      </footer>
    </div>

    <!-- I/O状态弹窗 -->
    <div id="ioStatusModal" class="modal-overlay">
      <div class="modal-container io-status-modal">
        <div class="modal-header">
          <h2><i class="fas fa-plug"></i>I/O状态监控</h2>
          <button class="modal-close-btn" onclick="closeIOStatusModal()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-content">
          <div class="io-status-grid">
            <!-- 输入IO状态 - 左侧 -->
            <div class="io-section input-section">
              <div class="io-section-header">
                <h3><i class="fas fa-sign-in-alt"></i>输入IO状态</h3>
              </div>
              <div class="io-columns">
                <!-- 第一列 -->
                <div class="io-column">
                  <div class="io-item">
                    <span class="io-label">启动按钮</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">停止按钮</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">复位按钮</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">备用按钮</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">急停按钮</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">SVG断路器</span>
                    <div class="io-indicator active">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">启动接触器</span>
                    <div class="io-indicator active">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">功率相序故障间</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">功率相序地刀间</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">远方/就地</span>
                    <div class="io-indicator active">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">外部故障</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">水冷系统电源故障</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">水冷系统预警</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">水冷系统停水故障</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">水冷系统停止</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                </div>
                <!-- 第二列 -->
                <div class="io-column">
                  <div class="io-item">
                    <span class="io-label">母联1状态输入</span>
                    <div class="io-indicator active">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">母联2状态输入</span>
                    <div class="io-indicator active">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">风机运行状态输入</span>
                    <div class="io-indicator active">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">IB04备用</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入21</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入22</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入23</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入24</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入25</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入26</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入27</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入28</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入29</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入30</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入31</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入32</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 输出IO状态 - 右侧 -->
            <div class="io-section output-section">
              <div class="io-section-header">
                <h3><i class="fas fa-sign-out-alt"></i>输出IO状态</h3>
              </div>
              <div class="io-columns">
                <!-- 第一列 -->
                <div class="io-column">
                  <div class="io-item">
                    <span class="io-label">SVG断路器分合闸命令</span>
                    <div class="io-indicator active">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">SVG断路器分闸命令</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">SVG断路器分闸命令</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">启动接触器合闸命令</span>
                    <div class="io-indicator active">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">启动接触器分闸命令</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">水冷系统启动命令</span>
                    <div class="io-indicator active">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">水冷系统停止命令</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出09</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">连跳电容器柜</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">数据指示灯</span>
                    <div class="io-indicator active">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">报警指示灯</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">故障指示灯</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">开放指示灯</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">备用指示灯</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">SVG故障</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                </div>
                <!-- 第二列 -->
                <div class="io-column">
                  <div class="io-item">
                    <span class="io-label">输出17</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出18</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出19</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出20</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出21</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出22</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出23</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出24</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出25</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出26</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出27</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出28</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出29</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出30</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出31</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出32</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 拓扑图弹窗 -->
    <div id="topologyModal" class="modal-overlay">
      <div class="modal-container topology-modal">
        <div class="modal-header">
          <h2 id="topologyModalTitle"><i class="fas fa-project-diagram"></i>系统拓扑图</h2>
          <button class="modal-close-btn" onclick="closeTopologyModal()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-content">
          <div class="topology-image-container">
            <img id="topologyImage" src="" alt="系统拓扑图" class="topology-image">
          </div>
          <div class="topology-iframe-container" id="topologyIframeContainer" style="display: none;">
            <iframe id="topologyIframe" src="" frameborder="0" class="topology-iframe"></iframe>
          </div>
        </div>
      </div>
    </div>

    <!-- 单元状态弹窗 -->
    <div id="unitStatusModal" class="modal-overlay">
      <div class="modal-container unit-status-modal">
        <div class="modal-header">
          <h2><i class="fas fa-microchip"></i>单元状态监控</h2>
          <button class="modal-close-btn" onclick="closeUnitStatusModal()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-content">
          <div class="unit-status-layout">
            <!-- 左侧单元列表 -->
            <div class="unit-list-section">
              <div class="unit-list-header">
                <h3>功能单元列表</h3>
              </div>
              <div class="unit-list">
                <div class="unit-item active" data-unit="A01">
                  <span class="unit-id">A01</span>
                  <span class="unit-voltage">740</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A02">
                  <span class="unit-id">A02</span>
                  <span class="unit-voltage">740</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A03">
                  <span class="unit-id">A03</span>
                  <span class="unit-voltage">738</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A04">
                  <span class="unit-id">A04</span>
                  <span class="unit-voltage">741</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A05">
                  <span class="unit-id">A05</span>
                  <span class="unit-voltage">741</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A06">
                  <span class="unit-id">A06</span>
                  <span class="unit-voltage">740</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A07">
                  <span class="unit-id">A07</span>
                  <span class="unit-voltage">741</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A08">
                  <span class="unit-id">A08</span>
                  <span class="unit-voltage">740</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A09">
                  <span class="unit-id">A09</span>
                  <span class="unit-voltage">741</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A10">
                  <span class="unit-id">A10</span>
                  <span class="unit-voltage">741</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A11">
                  <span class="unit-id">A11</span>
                  <span class="unit-voltage">741</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                  </div>
                </div>
                <div class="unit-item selected" data-unit="A12">
                  <span class="unit-id">A12</span>
                  <span class="unit-voltage">741</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 中间详细状态 -->
            <div class="unit-detail-section">
              <div class="unit-detail-header">
                <h3 id="selectedUnitTitle">A12 单元详细状态</h3>
                <button class="close-detail-btn" onclick="closeUnitDetail()">
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <div class="unit-detail-content">
                <div class="status-grid">
                  <div class="status-item">
                    <span class="status-label">下行光纤断</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">备用</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">封锁</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">超温</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">停止</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">欠压</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">启动</span>
                    <div class="status-indicator active">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">过压</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">电源故障</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">过流4</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">上行光纤断</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">过流3</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">下行光纤去同步</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">过流2</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">下行光纤无光</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">过流1</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧状态统计 -->
            <div class="unit-stats-section">
              <div class="stats-header">
                <h3>状态统计</h3>
                <div class="voltage-display">
                  <div class="voltage-item">
                    <span class="voltage-label">A相 电压最大</span>
                    <span class="voltage-value">741 V</span>
                  </div>
                  <div class="voltage-item">
                    <span class="voltage-label">电压最小</span>
                    <span class="voltage-value">738 V</span>
                  </div>
                  <div class="voltage-item">
                    <span class="voltage-label">B相 电压最大</span>
                    <span class="voltage-value">742 V</span>
                  </div>
                  <div class="voltage-item">
                    <span class="voltage-label">电压最小</span>
                    <span class="voltage-value">739 V</span>
                  </div>
                  <div class="voltage-item">
                    <span class="voltage-label">C相 电压最大</span>
                    <span class="voltage-value">743 V</span>
                  </div>
                  <div class="voltage-item">
                    <span class="voltage-label">电压最小</span>
                    <span class="voltage-value">738 V</span>
                  </div>
                </div>
              </div>
              <div class="unit-grid-display">
                <div class="grid-section">
                  <h4>B组单元</h4>
                  <div class="unit-mini-grid">
                    <div class="mini-unit" data-unit="B11">B11<br>740</div>
                    <div class="mini-unit" data-unit="B12">B12<br>742</div>
                  </div>
                </div>
                <div class="grid-section">
                  <h4>C组单元</h4>
                  <div class="unit-mini-grid">
                    <div class="mini-unit" data-unit="C11">C11<br>742</div>
                    <div class="mini-unit" data-unit="C12">C12<br>742</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>





    <!-- 登录弹窗 -->
    <div id="loginModal" class="modal-overlay">
      <div class="login-modal">
        <h3 style="text-align: center; color: var(--primary-color); margin-bottom: 20px;">
          <i class="fas fa-user-lock"></i> 用户登录
        </h3>
        <form class="login-form" onsubmit="handleLogin(event)">
          <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" name="username" value="bydq_admin" required>
          </div>
          <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" name="password" value="Aa123456" required>
          </div>
          <div class="login-buttons">
            <button type="button" class="btn" onclick="closeLoginModal()">取消</button>
            <button type="submit" class="btn primary">登录</button>
          </div>
        </form>
      </div>
    </div>

    <!-- 功能模块弹窗 -->
    <div id="moduleModal" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <div class="modal-header-left">
            <button id="debugMenuButton" class="debug-menu-btn" onclick="toggleDebugMenu()" title="显示菜单" style="display: none;">
              <i class="fas fa-bars"></i>
            </button>
          </div>
          <button class="modal-close-btn" onclick="closeModal()" title="关闭">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-content">
          <iframe id="moduleIframe" class="modal-iframe" src=""></iframe>
          <!-- 调试菜单 -->
          <div id="debugMenu" class="debug-menu" style="display: none;">
            <div class="debug-menu-content">
              <div class="debug-menu-list" id="debugMenuList">
                <!-- 菜单项将通过JavaScript动态生成 -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 权限配置弹窗 -->
    <div id="permissionModal" class="modal-overlay">
      <div class="permission-modal">
        <h3 style="text-align: center; color: var(--primary-color); margin-bottom: 20px;">
          <i class="fas fa-key"></i> 权限配置
        </h3>
        <div class="permission-list">
          <div class="permission-item">
            <span class="permission-label">版本信息</span>
            <div class="toggle-switch active" data-permission="version-info"></div>
          </div>
          <div class="permission-item">
            <span class="permission-label">参数曲线</span>
            <div class="toggle-switch active" data-permission="parameter-curve"></div>
          </div>
          <div class="permission-item">
            <span class="permission-label">故障录波</span>
            <div class="toggle-switch active" data-permission="fault-wave"></div>
          </div>
          <div class="permission-item">
            <span class="permission-label">DSP参数</span>
            <div class="toggle-switch active" data-permission="dsp"></div>
          </div>
          <div class="permission-item">
            <span class="permission-label">IO状态</span>
            <div class="toggle-switch active" data-permission="io-status"></div>
          </div>
          <div class="permission-item">
            <span class="permission-label">水冷系统</span>
            <div class="toggle-switch active" data-permission="cooling-topology"></div>
          </div>
          <div class="permission-item">
            <span class="permission-label">调试参数</span>
            <div class="toggle-switch active" data-permission="debug1"></div>
          </div>
        </div>
        <div class="login-buttons">
          <button type="button" class="btn" onclick="closePermissionModal()">取消</button>
          <button type="button" class="btn primary" onclick="savePermissions()">保存</button>
        </div>
      </div>
    </div>

    <script>
      /**
       * 桂林智源 SVG 数字化系统 - 触摸屏版本 JavaScript
       * 功能模块管理、登录控制和权限管理
       */

      // 全局变量
      let isLoggedIn = false;
      let currentUser = null;
      let currentDebugType = null;

      // 调试页面配置
      const debugPages = {
        'debug1': [
          { name: '设备操作', file: '设备操作.html' },
          { name: '系统参数', file: '系统参数.html' },
          { name: '控制模式', file: '控制模式.html' },
          { name: '滤波控制', file: '滤波控制.html' },
          { name: '保护参数', file: '保护参数.html' },
          { name: '保护使能', file: '保护使能.html' }
        ],
        'debug2': [
          { name: '设备操作', file: '设备操作.html' },
          { name: '系统参数', file: '系统参数.html' },
          { name: '控制模式', file: '控制模式.html' },
          { name: '控制参数一', file: '控制参数一.html' },
          { name: '控制参数二', file: '控制参数二.html' },
          { name: '谐波控制', file: '谐波控制.html' },
          { name: '旁路控制', file: '旁路控制.html' }
        ]
      };

      let permissions = {
        'version-info': false,
        'parameter-curve': false,
        'fault-wave': false,
        'dsp': false,
        'io-status': false,
        'cooling-topology': false,
        'debug1': false
      };

      // 模块配置映射
      const moduleConfig = {
        'electrical-topology': {
          title: '电气系统拓扑图',
          icon: 'fas fa-bolt',
          url: 'http://*************/scada/topo/fullscreen?guid=6f2b118e-1b27-4ebd-b1db-bcd08ce10bbf&type=3&date='
        },
        'cooling-topology': {
          title: '水冷系统拓扑图',
          icon: 'fas fa-tint',
          url: 'http://*************/scada/topo/fullscreen?guid=bdd07113-f2fd-4744-88f0-a055916c976b&type=3&date='
        },
        'io-status': {
          title: 'I/O状态监控',
          icon: 'fas fa-plug',
          url: 'http://*************/scada/topo/fullscreen?guid=df533b38-98b3-4c1a-9a3a-b2d7884f7770&type=3&date='
        },
        'unit-status': {
          title: '单元状态监控',
          icon: 'fas fa-microchip',
          url: 'http://*************/scada/topo/fullscreen?guid=bc305d60-29d2-4635-82bb-ead9b337b31d&type=3&date='
        },
        'history-event': {
          title: '历史事件',
          icon: 'fas fa-calendar-alt',
          url: '历史事件.html'
        },
        'parameter-curve': {
          title: '参数曲线',
          icon: 'fas fa-chart-area',
          url: '参数曲线.html'
        },
        'dsp': {
          title: 'DSP参数',
          icon: 'fas fa-microchip',
          url: 'http://*************/scada/topo/fullscreen?guid=721de54b-bed8-43dc-9157-81ac6cff32a4&type=3&date='
        },
        'fault-wave': {
          title: '故障录波',
          icon: 'fas fa-wave-square',
          url: '故障录波.html'
        },
        'version-info': {
          title: '版本信息',
          icon: 'fas fa-info-circle',
          url: 'http://*************/scada/topo/fullscreen?guid=eb900ac1-737c-4610-b4b3-ea05239531e3&type=3&date='
        }
      };

      /**
       * 更新时间显示
       */
      function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        });

        const timeDisplay = document.getElementById('currentTime');
        if (timeDisplay) {
          timeDisplay.textContent = timeString;
        }
      }







      /**
       * 检查登录状态
       * 页面加载时检查是否已有有效的登录token
       */
      async function checkLoginStatus() {
        const token = getTokenFromCookies();

        if (token) {
          // 验证token有效性
          const isValidToken = await validateToken(token);

          if (isValidToken) {
            isLoggedIn = true;
            currentUser = 'bydq_admin';

            // 更新UI
            updateLoginUI();

            // 显示登录后可见的菜单项
            showLoggedInMenuItems();
          } else {
            // 清除无效token
            deleteCookie('Admin-Token');
          }
        }
      }

      /**
       * 登录/注销切换
       */
      function toggleLogin() {
        if (isLoggedIn) {
          // 注销登录
          logout();
        } else {
          // 显示登录弹窗
          showLoginModal();
        }
      }

      /**
       * 显示登录弹窗
       */
      function showLoginModal() {
        const loginModal = document.getElementById('loginModal');
        loginModal.classList.add('show');

        // 聚焦到用户名输入框
        setTimeout(() => {
          const usernameInput = document.getElementById('username');
          if (usernameInput) {
            usernameInput.focus();
            // 如果用户名已填写，聚焦到密码框
            if (usernameInput.value.trim()) {
              document.getElementById('password').focus();
            }
          }
        }, 300);

        // 添加键盘事件监听
        addLoginKeyboardListeners();
      }

      /**
       * 添加登录表单键盘事件监听
       */
      function addLoginKeyboardListeners() {
        const loginModal = document.getElementById('loginModal');
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');

        // 回车键提交表单
        const handleEnterKey = (event) => {
          if (event.key === 'Enter') {
            event.preventDefault();
            const form = loginModal.querySelector('.login-form');
            if (form) {
              const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
              form.dispatchEvent(submitEvent);
            }
          }
        };

        // 为输入框添加回车键监听
        if (usernameInput) {
          usernameInput.addEventListener('keydown', handleEnterKey);
        }
        if (passwordInput) {
          passwordInput.addEventListener('keydown', handleEnterKey);
        }

        // ESC键关闭弹窗
        const handleEscKey = (event) => {
          if (event.key === 'Escape') {
            closeLoginModal();
          }
        };

        document.addEventListener('keydown', handleEscKey);

        // 存储事件监听器引用以便后续清理
        loginModal._keyboardListeners = {
          handleEnterKey,
          handleEscKey
        };
      }

      /**
       * 关闭登录弹窗
       */
      function closeLoginModal() {
        const loginModal = document.getElementById('loginModal');
        loginModal.classList.remove('show');

        // 清理键盘事件监听器
        if (loginModal._keyboardListeners) {
          const usernameInput = document.getElementById('username');
          const passwordInput = document.getElementById('password');

          if (usernameInput) {
            usernameInput.removeEventListener('keydown', loginModal._keyboardListeners.handleEnterKey);
          }
          if (passwordInput) {
            passwordInput.removeEventListener('keydown', loginModal._keyboardListeners.handleEnterKey);
          }

          document.removeEventListener('keydown', loginModal._keyboardListeners.handleEscKey);
          delete loginModal._keyboardListeners;
        }

        // 清空表单（可选）
        // document.getElementById('password').value = '';
      }

      /**
       * 处理登录
       */
      async function handleLogin(event) {
        event.preventDefault();

        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value.trim();



        // 显示加载状态
        const submitBtn = event.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '登录中...';
        submitBtn.disabled = true;

        try {
          // 检查是否已加载config.js中的loginUser函数
          if (typeof loginUser !== 'function') {
            showMessage('登录功能初始化失败，请刷新页面重试', 'error');
            return;
          }

          // 直接调用config.js中的登录API
          const result = await loginUser(username, password, '', 1);

          if (result.success && result.token) {
            // 登录成功
            isLoggedIn = true;
            currentUser = username;

            // 使用config.js中的updateAuthToken函数写入cookie（永久有效）
            updateAuthToken(result.token);

            // 更新UI
            updateLoginUI();

            // 显示登录后可见的菜单项
            showLoggedInMenuItems();

            // 关闭登录弹窗
            closeLoginModal();

            // 显示成功消息
            showMessage('登录成功！', 'success');
          } else {
            // 登录失败
            showMessage(result.message || '登录失败，请检查用户名和密码', 'error');

            // 清空密码输入框
            document.getElementById('password').value = '';

            // 聚焦到用户名输入框
            document.getElementById('username').focus();
          }
        } catch (error) {
          console.error('登录过程中发生错误:', error);
          showMessage('登录过程中发生错误，请稍后重试', 'error');

          // 清空密码输入框
          document.getElementById('password').value = '';

          // 聚焦到用户名输入框
          document.getElementById('username').focus();
        } finally {
          // 恢复按钮状态
          submitBtn.textContent = originalText;
          submitBtn.disabled = false;
        }
      }

      /**
       * 注销登录
       */
      function logout() {
        isLoggedIn = false;
        currentUser = null;

        // 删除认证cookie
        deleteCookie('Admin-Token');

        // 更新UI
        updateLoginUI();

        // 隐藏登录后可见的菜单项
        hideLoggedInMenuItems();

        showMessage('已注销登录', 'info');
        console.log('用户已注销登录');
      }

      /**
       * 更新登录UI
       */
      function updateLoginUI() {
        const loginBtn = document.getElementById('loginBtn');
        const loginText = document.getElementById('loginText');

        if (isLoggedIn) {
          loginText.textContent = '注销';
          loginBtn.style.background = 'linear-gradient(135deg, var(--success-color), var(--accent-color))';
        } else {
          loginText.textContent = '登录';
          loginBtn.style.background = 'linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary))';
        }
      }

      /**
       * 显示登录后可见的菜单项
       */
      function showLoggedInMenuItems() {
        const permissionItem = document.getElementById('permissionItem');
        const debug2Item = document.getElementById('debug2Item');

        if (permissionItem) {
          permissionItem.classList.remove('hidden');
        }

        if (debug2Item) {
          debug2Item.classList.remove('hidden');
        }
      }

      /**
       * 隐藏登录后可见的菜单项
       */
      function hideLoggedInMenuItems() {
        const permissionItem = document.getElementById('permissionItem');
        const debug2Item = document.getElementById('debug2Item');

        if (permissionItem) {
          permissionItem.classList.add('hidden');
        }

        if (debug2Item) {
          debug2Item.classList.add('hidden');
        }
      }

      /**
       * 显示消息提示
       */
      function showMessage(message, type = 'info') {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `message-toast ${type}`;
        messageEl.textContent = message;
        messageEl.style.cssText = `
          position: fixed;
          top: 150px;
          right: 30px;
          padding: 15px 25px;
          border-radius: 8px;
          color: white;
          font-weight: bold;
          z-index: 2000;
          opacity: 0;
          transform: translateX(100px);
          transition: all 0.3s ease;
        `;

        // 设置背景色
        switch (type) {
          case 'success':
            messageEl.style.background = 'var(--success-color)';
            break;
          case 'error':
            messageEl.style.background = 'var(--error-color)';
            break;
          case 'warning':
            messageEl.style.background = 'var(--warning-color)';
            break;
          default:
            messageEl.style.background = 'var(--primary-color)';
        }

        document.body.appendChild(messageEl);

        // 显示动画
        setTimeout(() => {
          messageEl.style.opacity = '1';
          messageEl.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
          messageEl.style.opacity = '0';
          messageEl.style.transform = 'translateX(100px)';
          setTimeout(() => {
            document.body.removeChild(messageEl);
          }, 300);
        }, 3000);
      }

      /**
       * 打开功能模块
       */
      function openModule(moduleId) {
        console.log(`打开模块: ${moduleId}`);

        // 检查权限（电气拓扑、单元状态、历史事件这三个菜单项不受权限控制）
        if (!['electrical-topology', 'unit-status', 'history-event'].includes(moduleId) && !permissions[moduleId]) {
          showMessage('您没有访问此模块的权限', 'warning');
          return;
        }

        const config = moduleConfig[moduleId];
        if (!config) {
          console.error(`未找到模块配置: ${moduleId}`);
          showMessage('模块配置错误', 'error');
          return;
        }

        // 获取弹窗元素
        const moduleIframe = document.getElementById('moduleIframe');
        const modalOverlay = document.getElementById('moduleModal');
        const menuButton = document.getElementById('debugMenuButton');

        // 构建URL
        let url = config.url;

        // 隐藏菜单按钮（非调试模块）
        if (menuButton) {
          menuButton.style.display = 'none';
        }

        // 重置当前调试类型
        currentDebugType = null;

        // 设置iframe源
        if (moduleIframe) {
          moduleIframe.src = url;
        }

        // 显示弹窗
        if (modalOverlay) {
          modalOverlay.classList.add('show');
        }

        // 添加触摸屏优化的事件监听
        addTouchOptimization();

        console.log(`已打开模块: ${config.title}`);
      }

      /**
       * 关闭弹窗
       */
      function closeModal() {
        const modalOverlay = document.getElementById('moduleModal');
        const moduleIframe = document.getElementById('moduleIframe');
        const menuButton = document.getElementById('debugMenuButton');

        modalOverlay.classList.remove('show');

        // 隐藏调试菜单和菜单按钮
        hideDebugMenu();
        if (menuButton) {
          menuButton.style.display = 'none';
        }

        // 重置当前调试类型
        currentDebugType = null;

        // 清空iframe源以释放资源
        setTimeout(() => {
          moduleIframe.src = '';
        }, 300);
      }

      /**
       * 显示调试菜单
       */
      function showDebugMenu(debugType) {
        if (!isLoggedIn && debugType === 'debug2') {
          showMessage('请先登录以访问此功能', 'warning');
          return;
        }

        // 设置当前调试类型
        currentDebugType = debugType;

        // 根据调试类型打开对应页面
        const debugUrl = debugType === 'debug1' ? 'debug1/设备操作.html' : 'debug2/设备操作.html';

        const moduleIframe = document.getElementById('moduleIframe');
        const modalOverlay = document.getElementById('moduleModal');
        const menuButton = document.getElementById('debugMenuButton');

        if (menuButton) {
          menuButton.style.display = 'block';
        }

        moduleIframe.src = debugUrl;
        modalOverlay.classList.add('show');
      }

      /**
       * 切换调试菜单显示
       */
      function toggleDebugMenu() {
        const debugMenu = document.getElementById('debugMenu');
        if (debugMenu.classList.contains('show')) {
          hideDebugMenu();
        } else {
          showDebugMenuList();
        }
      }

      /**
       * 显示调试菜单列表
       */
      function showDebugMenuList() {
        if (!currentDebugType) return;

        const debugMenu = document.getElementById('debugMenu');
        const debugMenuList = document.getElementById('debugMenuList');

        // 清空现有菜单项
        debugMenuList.innerHTML = '';

        // 获取当前调试类型的页面列表
        const pages = debugPages[currentDebugType] || [];

        // 生成菜单项
        pages.forEach((page, index) => {
          const menuItem = document.createElement('div');
          menuItem.className = 'debug-menu-item';
          menuItem.innerHTML = `
            <i class="fas fa-file-alt"></i>
            <span>${page.name}</span>
          `;
          menuItem.onclick = () => loadDebugPage(page.file);
          debugMenuList.appendChild(menuItem);
        });

        debugMenu.style.display = 'block';
        debugMenu.classList.add('show');
      }

      /**
       * 隐藏调试菜单
       */
      function hideDebugMenu() {
        const debugMenu = document.getElementById('debugMenu');
        debugMenu.classList.remove('show');
        setTimeout(() => {
          debugMenu.style.display = 'none';
        }, 300);
      }

      /**
       * 加载调试页面
       */
      function loadDebugPage(filename) {
        if (!currentDebugType) return;

        const moduleIframe = document.getElementById('moduleIframe');
        const debugUrl = `${currentDebugType}/${filename}`;

        moduleIframe.src = debugUrl;
        hideDebugMenu();
      }

      /**
       * 打开权限配置弹窗
       */
      function openPermissionConfig() {
        if (!isLoggedIn) {
          showMessage('请先登录以访问权限配置', 'warning');
          return;
        }

        const permissionModal = document.getElementById('permissionModal');
        permissionModal.classList.add('show');

        // 更新权限开关状态
        updatePermissionSwitches();
      }

      /**
       * 关闭权限配置弹窗
       */
      function closePermissionModal() {
        const permissionModal = document.getElementById('permissionModal');
        permissionModal.classList.remove('show');
      }

      /**
       * 更新权限开关状态
       */
      function updatePermissionSwitches() {
        const switches = document.querySelectorAll('.toggle-switch');
        switches.forEach(switchEl => {
          const permission = switchEl.getAttribute('data-permission');
          if (permissions[permission]) {
            switchEl.classList.add('active');
          } else {
            switchEl.classList.remove('active');
          }
        });
      }

      /**
       * 保存权限设置
       */
      function savePermissions() {
        const switches = document.querySelectorAll('.toggle-switch');
        switches.forEach(switchEl => {
          const permission = switchEl.getAttribute('data-permission');
          permissions[permission] = switchEl.classList.contains('active');
        });

        // 更新菜单项显示状态
        updateMenuVisibility();

        closePermissionModal();
        showMessage('权限设置已保存', 'success');
      }

      /**
       * 更新菜单项显示状态
       */
      function updateMenuVisibility() {
        console.log('=== updateMenuVisibility 函数被调用 ===');
        console.log('当前权限设置:', permissions);
        const navItems = document.querySelectorAll('.nav-item');
        console.log('找到', navItems.length, '个导航项');

        let hiddenCount = 0;
        let shownCount = 0;

        navItems.forEach(item => {
          const onclick = item.getAttribute('onclick');
          console.log('处理导航项，onclick属性:', onclick);

          let moduleId = null;
          if (onclick && onclick.includes('openModule')) {
            moduleId = onclick.match(/openModule\('([^']+)'\)/)?.[1];
          } else if (onclick && onclick.includes('showDebugMenu')) {
            moduleId = onclick.match(/showDebugMenu\('([^']+)'\)/)?.[1];
          }

          console.log('提取到模块ID:', moduleId, '权限:', permissions[moduleId]);

          // 权限和调试参数2菜单项不受权限配置控制，只受登录状态控制
          const isPermissionItem = item.id === 'permissionItem';
          const isDebug2Item = item.id === 'debug2Item';

          if (isPermissionItem || isDebug2Item) {
            // 这些项目只受登录状态控制，不受权限配置影响
            console.log('跳过权限项或调试参数2项:', item.id);
            return;
          }

          // 电气拓扑、单元状态、历史事件这三个菜单项不受权限控制
          if (moduleId && ['electrical-topology', 'unit-status', 'history-event'].includes(moduleId)) {
            item.style.display = 'flex';
            shownCount++;
            return;
          }

          // 其他菜单项根据权限设置显示/隐藏
          if (moduleId && permissions.hasOwnProperty(moduleId)) {
            if (permissions[moduleId]) {
              item.style.display = 'flex';
              item.classList.remove('hidden-by-permission');
              shownCount++;
            } else {
              item.style.display = 'none';
              item.classList.add('hidden-by-permission');
              hiddenCount++;
            }
          }
        });


      }

      /**
       * 添加触摸屏优化
       */
      function addTouchOptimization() {
        // 触摸屏优化代码，可以在这里添加特定的触摸事件处理
        console.log('添加触摸屏优化');
      }

      /**
       * 加载调试页面
       */
      function loadDebugPage(filename) {
        if (!currentDebugType) return;

        const moduleIframe = document.getElementById('moduleIframe');
        const debugUrl = `${currentDebugType}/${filename}`;

        moduleIframe.src = debugUrl;
        hideDebugMenu();

        console.log(`加载调试页面: ${debugUrl}`);
      }



      // 页面初始化
      document.addEventListener('DOMContentLoaded', function() {
        // 检查config.js是否正确加载
        const requiredFunctions = ['loginUser', 'getTokenFromCookies', 'updateAuthToken', 'getAlertDeviceList'];
        const missingFunctions = requiredFunctions.filter(func => typeof window[func] !== 'function');

        if (missingFunctions.length > 0) {
          console.error('必需函数未找到:', missingFunctions, '请检查config.js是否正确加载');
        }

        // 初始化时间显示
        updateTime();
        setInterval(updateTime, 1000);

        // 检查登录状态
        checkLoginStatus();

        // 添加权限开关点击事件
        const switches = document.querySelectorAll('.toggle-switch');
        switches.forEach(switchEl => {
          switchEl.addEventListener('click', function() {
            this.classList.toggle('active');
          });
        });

        // 根据权限设置更新菜单项显示状态
        updateMenuVisibility();

        // 延迟再次更新菜单项显示状态，确保所有元素都已加载
        setTimeout(() => {
          updateMenuVisibility();
        }, 500);

        // 定期检查和更新 MQTT 连接状态
        setInterval(() => {
          if (window.deviceOperationManager) {
            const manager = window.deviceOperationManager;
            if (typeof manager.updateMQTTStatus === 'function') {
              manager.updateMQTTStatus();
            }
          }
        }, 2000); // 每2秒检查一次 MQTT 状态

        // 初始化 MQTT 状态显示
        updateMQTTStatus('disconnected', 'MQTT 初始化中...');

        console.log('=== 触摸屏 MQTT 控制功能已启用 ===');
        console.log('可用控制函数：');
        console.log('- startOperation() - 启动操作 (发送 A1 信号)');
        console.log('- stopOperation() - 停止操作 (发送 A2 信号)');
        console.log('- resetOperation() - 复位操作 (发送 A3 信号)');
        console.log('- updateFooterStatus(message, type) - 更新状态显示');
      });
    </script>
  </body>
</html>
