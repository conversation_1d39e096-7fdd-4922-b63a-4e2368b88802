<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>调试参数2 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="logo.png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 100%);
            color: #ffffff;
            width: 100vw;
            min-height: 100vh;
            overflow-x: hidden;
            overflow-y: auto;
            position: relative;
        }

        /* 科技感背景动画 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
            background-attachment: fixed;
        }

        .container {
            width: 100%;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            padding: 30px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 48px;
            color: #00d4ff;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .header p {
            font-size: 24px;
            color: #b8c5d6;
            opacity: 0.8;
        }

        .params-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            flex: 1;
            padding: 0 40px;
        }

        .param-card {
            background: linear-gradient(135deg, rgba(26, 31, 46, 0.9), rgba(42, 49, 66, 0.9));
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s ease;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            min-height: 280px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .param-card.disabled {
            background: linear-gradient(135deg, rgba(60, 60, 60, 0.6), rgba(80, 80, 80, 0.6));
            border-color: rgba(120, 120, 120, 0.3);
            cursor: not-allowed;
            opacity: 0.6;
        }

        .param-card.disabled .param-icon {
            color: #888888;
        }

        .param-card.disabled .param-title {
            color: #aaaaaa;
        }



        .param-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .param-card:not(.disabled):hover::before {
            left: 100%;
        }

        .param-card:not(.disabled):hover {
            transform: translateY(-10px) scale(1.02);
            border-color: #00d4ff;
            box-shadow: 0 20px 40px rgba(0, 212, 255, 0.3);
        }

        .param-card:not(.disabled):active {
            transform: translateY(-5px) scale(0.98);
        }

        .param-icon {
            font-size: 56px;
            color: #00d4ff;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .param-title {
            font-size: 32px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 0;
        }

        .development-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            color: white;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: bold;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            border-top: 1px solid rgba(0, 212, 255, 0.3);
        }

        .footer p {
            color: #7a8ba0;
            font-size: 16px;
        }

        /* 加载动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .param-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .param-card:nth-child(1) { animation-delay: 0.1s; }
        .param-card:nth-child(2) { animation-delay: 0.2s; }
        .param-card:nth-child(3) { animation-delay: 0.3s; }
        .param-card:nth-child(4) { animation-delay: 0.4s; }
        .param-card:nth-child(5) { animation-delay: 0.5s; }
        .param-card:nth-child(6) { animation-delay: 0.6s; }
        .param-card:nth-child(7) { animation-delay: 0.7s; }
        .param-card:nth-child(8) { animation-delay: 0.8s; }

        /* 响应式调整 */
        @media (min-width: 1920px) {
            .params-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 30px;
            }
            
            .param-card {
                padding: 30px;
                min-height: 280px;
            }
            
            .param-icon {
                font-size: 56px;
            }
            
            .param-title {
                font-size: 32px;
            }
        }
        
        @media (max-width: 1919px) and (min-width: 1601px) {
            .params-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 25px;
            }
            
            .param-card {
                padding: 25px;
                min-height: 260px;
            }
            
            .param-icon {
                font-size: 50px;
            }
            
            .param-title {
                font-size: 30px;
            }
        }
        
        @media (max-width: 1600px) and (min-width: 1367px) {
            .params-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 25px;
            }
            
            .param-card {
                padding: 25px;
                min-height: 240px;
            }
            
            .param-icon {
                font-size: 48px;
                margin-bottom: 15px;
            }
            
            .param-title {
                font-size: 28px;
            }
        }
        
        @media (max-width: 1366px) {
            .params-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }
            
            .param-card {
                padding: 20px;
                min-height: 220px;
            }
            
            .param-icon {
                font-size: 42px;
                margin-bottom: 15px;
            }
            
            .param-title {
                font-size: 24px;
            }
            
            .header {
                margin-bottom: 30px;
            }
            
            .header h1 {
                font-size: 42px;
            }
            
            .header p {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-tools"></i> 调试参数2</h1>
            <p>高级调试与控制参数配置管理界面</p>
        </div>

        <div class="params-grid">
            <div class="param-card" onclick="openDeviceOperation2()">
                <div class="param-icon">
                    <i class="fas fa-desktop"></i>
                </div>
                <div class="param-title">设备操作</div>
            </div>

            <div class="param-card" onclick="openControlMode2()">
                <div class="param-icon">
                    <i class="fas fa-sliders-h"></i>
                </div>
                <div class="param-title">控制模式</div>
            </div>

            <div class="param-card" onclick="openControlParams1()">
                <div class="param-icon">
                    <i class="fas fa-adjust"></i>
                </div>
                <div class="param-title">控制参数1</div>
            </div>

            <div class="param-card disabled" onclick="showDevelopmentMessage('控制参数2')">
                <div class="development-badge">开发中</div>
                <div class="param-icon">
                    <i class="fas fa-adjust"></i>
                </div>
                <div class="param-title">控制参数2</div>
            </div>

            <div class="param-card" onclick="openSystemParams2()">
                <div class="param-icon">
                    <i class="fas fa-server"></i>
                </div>
                <div class="param-title">系统参数</div>
            </div>

            <div class="param-card" onclick="openHarmonicControl()">
                <div class="param-icon">
                    <i class="fas fa-wave-square"></i>
                </div>
                <div class="param-title">谐波控制</div>
            </div>

            <div class="param-card disabled" onclick="showDevelopmentMessage('录波参数')">
                <div class="development-badge">待开发</div>
                <div class="param-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="param-title">录波参数</div>
            </div>

            <div class="param-card disabled" onclick="showDevelopmentMessage('旁路控制')">
                <div class="development-badge">待开发</div>
                <div class="param-icon">
                    <i class="fas fa-route"></i>
                </div>
                <div class="param-title">旁路控制</div>
            </div>
        </div>

        <div class="footer">
            <p>© 2025 桂林智源 - SVG 数字化系统 调试参数2界面</p>
        </div>
    </div>

    <script>
        /**
         * 设备操作
         */
        function openDeviceOperation2() {
            console.log('打开设备操作页面');

            // 通知父窗口记录导航状态（如果在iframe中）
            try {
                if (window.parent && window.parent !== window) {
                    // 使用父窗口的全局函数记录导航
                    if (typeof window.parent.recordIframeNavigation === 'function') {
                        window.parent.recordIframeNavigation(
                            'http://*************/scada/topo/fullscreen?guid=a77103b4-e76c-4570-8f6b-46b9a928816c&type=3',
                            '设备操作'
                        );
                        console.log('已通知父窗口记录导航状态');
                    } else {
                        console.log('父窗口不支持导航记录功能');
                    }
                }
            } catch (error) {
                console.log('无法通知父窗口（可能跨域）:', error.message);
            }

            window.location.href = 'http://*************/scada/topo/fullscreen?guid=a77103b4-e76c-4570-8f6b-46b9a928816c&type=3';
        }

        /**
         * 通用导航记录函数
         * @param {string} url - 目标URL
         * @param {string} title - 页面标题
         */
        function recordNavigation(url, title) {
            try {
                if (window.parent && window.parent !== window) {
                    if (typeof window.parent.recordIframeNavigation === 'function') {
                        window.parent.recordIframeNavigation(url, title);
                        console.log(`已通知父窗口记录导航状态: ${title}`);
                    } else {
                        console.log('父窗口不支持导航记录功能');
                    }
                }
            } catch (error) {
                console.log('无法通知父窗口（可能跨域）:', error.message);
            }
        }

        /**
         * 控制模式
         */
        function openControlMode2() {
            console.log('打开控制模式页面');
            const url = 'http://*************/scada/topo/fullscreen?guid=b91240f1-a7f0-481c-98cf-ac8097c99460&type=3';
            recordNavigation(url, '控制模式');
            window.location.href = url;
        }

        /**
         * 控制参数1
         */
        function openControlParams1() {
            console.log('打开控制参数1页面');
            const url = 'http://*************/scada/topo/fullscreen?guid=756ce8ea-2069-46ba-b2f0-d86e8bce253a&type=3';
            recordNavigation(url, '控制参数1');
            window.location.href = url;
        }

        /**
         * 系统参数
         */
        function openSystemParams2() {
            console.log('打开系统参数页面');
            const url = 'http://*************/scada/topo/fullscreen?guid=bbbbdc58-faa2-4199-85b5-9eedd39578fc&type=3';
            recordNavigation(url, '系统参数');
            window.location.href = url;
        }

        /**
         * 谐波控制
         */
        function openHarmonicControl() {
            console.log('打开谐波控制页面');
            const url = 'http://*************/scada/topo/fullscreen?guid=729e479e-8e86-4e2b-973d-e24ff1de2bf0&type=3';
            recordNavigation(url, '谐波控制');
            window.location.href = url;
        }

        /**
         * 显示开发中提示信息
         */
        function showDevelopmentMessage(moduleName) {
            alert(`${moduleName}功能正在开发中，敬请期待！`);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('调试参数2页面加载完成');
        });
    </script>
</body>
</html>
